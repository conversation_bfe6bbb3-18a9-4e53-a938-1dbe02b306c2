# Sistem Penilaian Kelayakan Kost - Universitas Pamulang

Aplikasi web untuk membantu mahasiswa Universitas Pamulang dalam menilai dan membandingkan kelayakan tempat kost berdasarkan sistem scoring yang objektif.

## Fitur Utama

- **Sistem Scoring Otomatis**: Penilaian kost berdasarkan kriteria dan bobot yang dapat dikonfigurasi
- **Pencarian & Filter**: Cari kost berdasarkan harga, jarak, fasilitas, dan skor
- **Review System**: Mahasiswa dapat memberikan review dan rating
- **Admin Panel**: Kelola data kost, kriteria penilaian, dan review
- **Responsive Design**: Tampilan optimal di desktop, tablet, dan mobile
- **Maps Integration**: Lokasi kost ditampilkan dengan Google Maps

## Teknologi yang Digunakan

- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Maps**: Google Maps Static API

## Instalasi

### Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Web server (Apache/Nginx)
- Extension PHP: PDO, PDO_MySQL

### Langkah Instalasi

1. **Clone atau Download Project**
   ```bash
   git clone [repository-url]
   cd Projek\ Bowok
   ```

2. **Setup Database**
   - Buat database MySQL baru
   - Import file `database/schema.sql`
   ```sql
   CREATE DATABASE kost_evaluation_system;
   USE kost_evaluation_system;
   SOURCE database/schema.sql;
   ```

3. **Konfigurasi Database**
   - Edit file `config/config.php`
   - Sesuaikan pengaturan database:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'kost_evaluation_system');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

4. **Setup Google Maps API (Opsional)**
   - Dapatkan API key dari Google Cloud Console
   - Edit `config/config.php`:
   ```php
   define('GOOGLE_MAPS_API_KEY', 'your_api_key_here');
   ```

5. **Setup Permissions**
   ```bash
   chmod 755 uploads/
   chmod 644 config/config.php
   ```

6. **Akses Aplikasi**
   - Buka browser dan akses: `http://localhost/Projek%20Bowok/`
   - Login admin: `http://localhost/Projek%20Bowok/admin/login.php`
     - Username: `admin`
     - Password: `admin123`

## Struktur Project

```
Projek Bowok/
├── admin/                  # Admin panel
│   ├── login.php          # Login admin
│   ├── dashboard.php      # Dashboard admin
│   ├── boarding-houses.php # Kelola kost
│   └── logout.php         # Logout
├── assets/                # Asset statis
│   ├── css/
│   │   └── style.css      # Custom CSS
│   ├── js/
│   │   └── main.js        # Custom JavaScript
│   └── images/            # Gambar
├── classes/               # PHP Classes
│   ├── BoardingHouse.php  # Class untuk kost
│   ├── Review.php         # Class untuk review
│   └── ScoringSystem.php  # Class untuk scoring
├── config/                # Konfigurasi
│   ├── config.php         # Konfigurasi utama
│   └── database.php       # Koneksi database
├── database/              # Database
│   └── schema.sql         # Schema database
├── uploads/               # Upload files
├── index.php              # Halaman utama
├── detail.php             # Detail kost
├── add-review.php         # Tambah review
└── README.md              # Dokumentasi
```

## Penggunaan

### Untuk Mahasiswa

1. **Mencari Kost**
   - Akses halaman utama
   - Gunakan filter pencarian (harga, jarak, fasilitas)
   - Lihat daftar kost dengan skor dan rating

2. **Melihat Detail Kost**
   - Klik "Lihat Detail" pada kost yang diminati
   - Lihat informasi lengkap, foto, lokasi, dan review

3. **Memberikan Review**
   - Klik "Beri Review" pada detail kost
   - Isi form review dengan rating dan komentar

### Untuk Admin

1. **Login Admin**
   - Akses `/admin/login.php`
   - Login dengan username dan password admin

2. **Mengelola Kost**
   - Tambah, edit, atau hapus data kost
   - Atur skor untuk setiap kriteria penilaian

3. **Mengelola Review**
   - Approve atau reject review dari mahasiswa
   - Hapus review yang tidak sesuai

## Konfigurasi Sistem Scoring

Sistem scoring menggunakan weighted scoring dengan kriteria default:

- **Harga Sewa** (Bobot: 2.5)
- **Jarak ke Kampus** (Bobot: 2.5)
- **Keamanan** (Bobot: 2.0)
- **Fasilitas** (Bobot: 1.5 per fasilitas)
- **Kebersihan** (Bobot: 1.5)
- **Lingkungan** (Bobot: 1.0)

Skor total dihitung dengan rumus:
```
Skor Total = Σ (Nilai Kriteria × Bobot Kriteria) × 20 + (Rating Review × 30%)
```

## Troubleshooting

### Database Connection Error
- Pastikan MySQL service berjalan
- Periksa konfigurasi database di `config/config.php`
- Pastikan user database memiliki privilege yang cukup

### Permission Denied
- Pastikan folder `uploads/` memiliki permission write
- Periksa ownership file dan folder

### Google Maps Tidak Muncul
- Pastikan API key Google Maps sudah benar
- Enable Google Maps Static API di Google Cloud Console
- Periksa quota dan billing account

## Kontribusi

Untuk berkontribusi pada project ini:

1. Fork repository
2. Buat branch fitur baru
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request

## Lisensi

Project ini dikembangkan untuk keperluan akademik Universitas Pamulang.

## Kontak

Untuk pertanyaan atau dukungan, hubungi tim pengembang melalui:
- Email: [email-contact]
- GitHub Issues: [repository-issues-url]
