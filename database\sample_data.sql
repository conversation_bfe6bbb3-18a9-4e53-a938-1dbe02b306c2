-- Sample Data for Testing
-- Data contoh untuk testing sistem penilaian kost

USE kost_evaluation_system;

-- Insert sample boarding houses
INSERT INTO boarding_houses (name, address, latitude, longitude, distance_to_campus, monthly_price, description, facilities, contact_phone, contact_whatsapp, owner_name, images, total_score) VALUES
('Kost Melati Indah', 'Jl. Melati No. 15, Pamulang Timur, Tangerang Selatan', -6.3420, 106.7580, 1.2, 800000, 'Kost nyaman dan bersih dengan fasilitas lengkap. Dekat dengan kampus Universitas Pamulang dan akses transportasi mudah.', '["WiFi", "<PERSON>mar <PERSON>", "Laundry", "Dapur", "Parkir Motor", "CCTV"]', '021-12345678', '081234567890', '<PERSON><PERSON>', '["assets/images/no-image.jpg"]', 85.5),

('Kost Mawar Residence', 'Jl. <PERSON>war <PERSON>a No. 22, <PERSON><PERSON>ng Barat, Tangerang Selatan', -6.3380, 106.7520, 0.8, 1200000, 'Kost eksklusif dengan fasilitas premium. Lingkungan tenang dan aman, cocok untuk mahasiswa yang mengutamakan kenyamanan.', '["WiFi", "Kamar Mandi Dalam", "AC", "Laundry", "Dapur", "Parkir Motor", "Parkir Mobil", "CCTV", "Satpam"]', '021-87654321', '081987654321', 'Bapak Andi', '["assets/images/no-image.jpg"]', 92.3),

('Kost Anggrek Putih', 'Jl. Anggrek No. 8, Pamulang, Tangerang Selatan', -6.3350, 106.7600, 2.1, 600000, 'Kost sederhana dengan harga terjangkau. Cocok untuk mahasiswa dengan budget terbatas namun tetap nyaman.', '["WiFi", "Kamar Mandi Luar", "Dapur", "Parkir Motor"]', '021-11223344', '082112233445', 'Ibu Rina', '["assets/images/no-image.jpg"]', 72.8),

('Kost Dahlia Premium', 'Jl. Dahlia Indah No. 5, Pamulang Timur, Tangerang Selatan', -6.3400, 106.7550, 1.5, 1500000, 'Kost mewah dengan fasilitas hotel. Dilengkapi dengan gym, kolam renang mini, dan area study yang nyaman.', '["WiFi", "Kamar Mandi Dalam", "AC", "Laundry", "Dapur", "Parkir Motor", "Parkir Mobil", "CCTV", "Satpam", "Gym"]', '021-55667788', '081556677889', 'Bapak Dedi', '["assets/images/no-image.jpg"]', 95.2),

('Kost Tulip Asri', 'Jl. Tulip No. 12, Pamulang Barat, Tangerang Selatan', -6.3330, 106.7480, 1.8, 750000, 'Kost dengan suasana asri dan sejuk. Dikelilingi taman yang indah dan udara yang segar.', '["WiFi", "Kamar Mandi Dalam", "Laundry", "Dapur", "Parkir Motor", "Taman"]', '021-99887766', '081998877665', 'Ibu Dewi', '["assets/images/no-image.jpg"]', 78.9);

-- Insert scores for boarding houses
INSERT INTO boarding_house_scores (boarding_house_id, criteria_id, score) VALUES
-- Kost Melati Indah (ID: 1)
(1, 1, 4), -- Harga Sewa
(1, 2, 5), -- Jarak ke Kampus
(1, 3, 4), -- Keamanan
(1, 4, 5), -- Fasilitas Kamar Mandi
(1, 5, 4), -- WiFi
(1, 6, 4), -- Fasilitas Laundry
(1, 7, 4), -- Dapur Bersama
(1, 8, 4), -- Listrik
(1, 9, 4), -- Kebersihan
(1, 10, 4), -- Lingkungan Tenang
(1, 11, 4), -- Lingkungan Ramah

-- Kost Mawar Residence (ID: 2)
(2, 1, 3), -- Harga Sewa
(2, 2, 5), -- Jarak ke Kampus
(2, 3, 5), -- Keamanan
(2, 4, 5), -- Fasilitas Kamar Mandi
(2, 5, 5), -- WiFi
(2, 6, 5), -- Fasilitas Laundry
(2, 7, 5), -- Dapur Bersama
(2, 8, 5), -- Listrik
(2, 9, 5), -- Kebersihan
(2, 10, 5), -- Lingkungan Tenang
(2, 11, 5), -- Lingkungan Ramah

-- Kost Anggrek Putih (ID: 3)
(3, 1, 5), -- Harga Sewa
(3, 2, 3), -- Jarak ke Kampus
(3, 3, 3), -- Keamanan
(3, 4, 2), -- Fasilitas Kamar Mandi
(3, 5, 3), -- WiFi
(3, 6, 2), -- Fasilitas Laundry
(3, 7, 3), -- Dapur Bersama
(3, 8, 3), -- Listrik
(3, 9, 3), -- Kebersihan
(3, 10, 3), -- Lingkungan Tenang
(3, 11, 4), -- Lingkungan Ramah

-- Kost Dahlia Premium (ID: 4)
(4, 1, 2), -- Harga Sewa
(4, 2, 4), -- Jarak ke Kampus
(4, 3, 5), -- Keamanan
(4, 4, 5), -- Fasilitas Kamar Mandi
(4, 5, 5), -- WiFi
(4, 6, 5), -- Fasilitas Laundry
(4, 7, 5), -- Dapur Bersama
(4, 8, 5), -- Listrik
(4, 9, 5), -- Kebersihan
(4, 10, 5), -- Lingkungan Tenang
(4, 11, 5), -- Lingkungan Ramah

-- Kost Tulip Asri (ID: 5)
(5, 1, 4), -- Harga Sewa
(5, 2, 4), -- Jarak ke Kampus
(5, 3, 4), -- Keamanan
(5, 4, 4), -- Fasilitas Kamar Mandi
(5, 5, 4), -- WiFi
(5, 6, 3), -- Fasilitas Laundry
(5, 7, 4), -- Dapur Bersama
(5, 8, 4), -- Listrik
(5, 9, 4), -- Kebersihan
(5, 10, 5), -- Lingkungan Tenang
(5, 11, 4); -- Lingkungan Ramah

-- Insert sample reviews
INSERT INTO reviews (boarding_house_id, reviewer_name, reviewer_email, student_id, rating, review_text, lived_duration_months, is_approved) VALUES
(1, 'Ahmad Rizki', '<EMAIL>', '2021010001', 4, 'Kost yang nyaman dan bersih. Pemilik ramah dan fasilitas lengkap. WiFi cepat dan cocok untuk kuliah online.', 12, 1),
(1, 'Siti Nurhaliza', '<EMAIL>', '2021010002', 5, 'Sangat puas tinggal di sini. Lokasi strategis dekat kampus, kamar bersih, dan harga terjangkau.', 8, 1),
(1, 'Budi Santoso', '<EMAIL>', '2020010015', 4, 'Kost yang recommended untuk mahasiswa. Suasana tenang untuk belajar dan pemilik sangat baik.', 18, 1),

(2, 'Diana Putri', '<EMAIL>', '2021010003', 5, 'Kost premium dengan fasilitas terbaik! AC dingin, kamar luas, dan keamanan 24 jam. Worth it!', 6, 1),
(2, 'Eko Prasetyo', '<EMAIL>', '2020010020', 5, 'Meskipun harga agak mahal, tapi fasilitas sangat memuaskan. Parkir luas dan ada satpam.', 10, 1),

(3, 'Fitri Handayani', '<EMAIL>', '2021010004', 3, 'Harga murah tapi ya sesuai dengan fasilitasnya. Kamar mandi luar agak kurang nyaman, tapi overall oke untuk budget terbatas.', 4, 1),
(3, 'Galih Pratama', '<EMAIL>', '2021010005', 4, 'Cocok untuk mahasiswa yang budget pas-pasan. Pemilik baik dan lokasi masih terjangkau ke kampus.', 7, 1),

(4, 'Hana Safitri', '<EMAIL>', '2020010025', 5, 'Kost terbaik yang pernah saya tempati! Fasilitas lengkap seperti hotel, gym tersedia, dan pelayanan excellent.', 12, 1),
(4, 'Indra Gunawan', '<EMAIL>', '2019010010', 5, 'Mahal tapi sebanding dengan kualitas. Cocok untuk yang mengutamakan kenyamanan dan fasilitas premium.', 24, 1),

(5, 'Joko Widodo', '<EMAIL>', '2021010006', 4, 'Suasana asri dan tenang, cocok untuk belajar. Taman di sekitar kost membuat udara sejuk.', 9, 1),
(5, 'Kartika Sari', '<EMAIL>', '2020010030', 4, 'Lingkungan nyaman dan hijau. Harga reasonable dengan fasilitas yang cukup lengkap.', 15, 1);

-- Update boarding house statistics
UPDATE boarding_houses bh SET 
    average_rating = (SELECT AVG(rating) FROM reviews WHERE boarding_house_id = bh.id AND is_approved = 1),
    review_count = (SELECT COUNT(*) FROM reviews WHERE boarding_house_id = bh.id AND is_approved = 1);

-- Recalculate total scores (this would normally be done by the ScoringSystem class)
-- The scores above are manually calculated examples
