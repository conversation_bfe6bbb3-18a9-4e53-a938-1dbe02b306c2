<?php
/**
 * Main Index Page
 * Halaman utama untuk pencarian dan daftar kost
 */

require_once 'config/config.php';
require_once 'classes/BoardingHouse.php';
require_once 'classes/ScoringSystem.php';

$boardingHouse = new BoardingHouse();
$scoring = new ScoringSystem();

// Get filters from URL parameters
$filters = [];
if (!empty($_GET['search'])) {
    $filters['search'] = sanitize_input($_GET['search']);
}
if (!empty($_GET['min_price'])) {
    $filters['min_price'] = (float)$_GET['min_price'];
}
if (!empty($_GET['max_price'])) {
    $filters['max_price'] = (float)$_GET['max_price'];
}
if (!empty($_GET['max_distance'])) {
    $filters['max_distance'] = (float)$_GET['max_distance'];
}
if (!empty($_GET['min_score'])) {
    $filters['min_score'] = (float)$_GET['min_score'];
}
if (!empty($_GET['facilities'])) {
    $filters['facilities'] = $_GET['facilities'];
}

// Get sorting parameters
$sort = !empty($_GET['sort']) ? sanitize_input($_GET['sort']) : 'total_score';
$order = !empty($_GET['order']) ? sanitize_input($_GET['order']) : 'DESC';

// Pagination
$page = !empty($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * ITEMS_PER_PAGE;

// Get boarding houses
$boarding_houses = $boardingHouse->getAll($filters, $sort, $order, ITEMS_PER_PAGE, $offset);
$total_count = $boardingHouse->getCount($filters);
$total_pages = ceil($total_count / ITEMS_PER_PAGE);

// Get criteria for filter options
$criteria = $scoring->getCriteria();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Universitas Pamulang</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-home"></i> KostEval
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add-review.php">Beri Review</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/login.php">Admin</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-5 fw-bold text-primary">Temukan Kost Terbaik</h1>
                    <p class="lead">Sistem penilaian kelayakan kost untuk mahasiswa Universitas Pamulang</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="container my-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search"></i> Pencarian & Filter
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="index.php">
                    <div class="row g-3">
                        <!-- Search -->
                        <div class="col-md-6">
                            <label class="form-label">Cari Kost</label>
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Nama kost atau alamat..." 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                        </div>
                        
                        <!-- Price Range -->
                        <div class="col-md-3">
                            <label class="form-label">Harga Min</label>
                            <input type="number" class="form-control" name="min_price" 
                                   placeholder="Rp 0" 
                                   value="<?php echo htmlspecialchars($_GET['min_price'] ?? ''); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Harga Max</label>
                            <input type="number" class="form-control" name="max_price" 
                                   placeholder="Rp 5,000,000" 
                                   value="<?php echo htmlspecialchars($_GET['max_price'] ?? ''); ?>">
                        </div>
                        
                        <!-- Distance and Score -->
                        <div class="col-md-3">
                            <label class="form-label">Jarak Max (km)</label>
                            <input type="number" step="0.1" class="form-control" name="max_distance" 
                                   placeholder="10" 
                                   value="<?php echo htmlspecialchars($_GET['max_distance'] ?? ''); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Skor Min</label>
                            <input type="number" class="form-control" name="min_score" 
                                   placeholder="0" 
                                   value="<?php echo htmlspecialchars($_GET['min_score'] ?? ''); ?>">
                        </div>
                        
                        <!-- Facilities -->
                        <div class="col-md-6">
                            <label class="form-label">Fasilitas</label>
                            <div class="row">
                                <?php
                                $facilities_options = ['WiFi', 'Kamar Mandi Dalam', 'Laundry', 'Dapur', 'AC', 'Parkir'];
                                $selected_facilities = $_GET['facilities'] ?? [];
                                foreach ($facilities_options as $facility): ?>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="facilities[]" 
                                               value="<?php echo $facility; ?>" id="facility_<?php echo str_replace(' ', '_', $facility); ?>"
                                               <?php echo in_array($facility, $selected_facilities) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="facility_<?php echo str_replace(' ', '_', $facility); ?>">
                                            <?php echo $facility; ?>
                                        </label>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Sort -->
                        <div class="col-md-6">
                            <label class="form-label">Urutkan</label>
                            <select class="form-select" name="sort">
                                <option value="total_score" <?php echo $sort === 'total_score' ? 'selected' : ''; ?>>Skor Tertinggi</option>
                                <option value="monthly_price" <?php echo $sort === 'monthly_price' ? 'selected' : ''; ?>>Harga Terendah</option>
                                <option value="distance_to_campus" <?php echo $sort === 'distance_to_campus' ? 'selected' : ''; ?>>Jarak Terdekat</option>
                                <option value="average_rating" <?php echo $sort === 'average_rating' ? 'selected' : ''; ?>>Rating Tertinggi</option>
                                <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Terbaru</option>
                            </select>
                            <input type="hidden" name="order" value="<?php echo $sort === 'monthly_price' || $sort === 'distance_to_campus' ? 'ASC' : 'DESC'; ?>">
                        </div>
                        
                        <!-- Submit -->
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Cari
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="container my-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Daftar Kost (<?php echo $total_count; ?> hasil)</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active" id="grid-view">
                    <i class="fas fa-th"></i>
                </button>
                <button type="button" class="btn btn-outline-primary" id="list-view">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        
        <div class="row" id="boarding-houses-container">
            <?php if (empty($boarding_houses)): ?>
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> Tidak ada kost yang ditemukan dengan kriteria pencarian Anda.
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($boarding_houses as $house): ?>
                    <div class="col-lg-4 col-md-6 mb-4 boarding-house-card">
                        <div class="card h-100 shadow-sm">
                            <?php 
                            $images = json_decode($house['images'], true);
                            $first_image = !empty($images) ? $images[0] : 'assets/images/no-image.jpg';
                            ?>
                            <img src="<?php echo $first_image; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($house['name']); ?>" style="height: 200px; object-fit: cover;">
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($house['name']); ?></h5>
                                <p class="card-text text-muted">
                                    <i class="fas fa-map-marker-alt"></i> 
                                    <?php echo htmlspecialchars(substr($house['address'], 0, 50)) . (strlen($house['address']) > 50 ? '...' : ''); ?>
                                </p>
                                
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="score-badge bg-primary text-white rounded p-2">
                                            <div class="fw-bold"><?php echo number_format($house['total_score'], 1); ?></div>
                                            <small>Skor</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-success">
                                            <div class="fw-bold">Rp <?php echo number_format($house['monthly_price'], 0, ',', '.'); ?></div>
                                            <small>per bulan</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-info">
                                            <div class="fw-bold"><?php echo number_format($house['distance_to_campus'], 1); ?> km</div>
                                            <small>ke kampus</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($house['avg_rating']): ?>
                                <div class="mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="stars me-2">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?php echo $i <= $house['avg_rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                        <small class="text-muted">(<?php echo $house['review_count']; ?> review)</small>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php 
                                $facilities = json_decode($house['facilities'], true);
                                if (!empty($facilities)): ?>
                                <div class="mb-3">
                                    <?php foreach (array_slice($facilities, 0, 3) as $facility): ?>
                                        <span class="badge bg-light text-dark me-1"><?php echo htmlspecialchars($facility); ?></span>
                                    <?php endforeach; ?>
                                    <?php if (count($facilities) > 3): ?>
                                        <span class="badge bg-secondary">+<?php echo count($facilities) - 3; ?> lainnya</span>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <a href="detail.php?id=<?php echo $house['id']; ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-eye"></i> Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                            <i class="fas fa-chevron-left"></i> Sebelumnya
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                            Selanjutnya <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo APP_NAME; ?></h5>
                    <p>Membantu mahasiswa Universitas Pamulang menemukan kost terbaik dengan sistem penilaian yang objektif.</p>
                </div>
                <div class="col-md-6">
                    <h5>Kontak</h5>
                    <p>
                        <i class="fas fa-university"></i> Universitas Pamulang<br>
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <small>&copy; 2024 <?php echo APP_NAME; ?>. Dikembangkan untuk Universitas Pamulang.</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
