-- Database Schema for Boarding House Evaluation System
-- Sistem Penilaian Ke<PERSON> Kost untuk Mahasiswa Universitas Pamulang

CREATE DATABASE IF NOT EXISTS kost_evaluation_system;
USE kost_evaluation_system;

-- Table for admin users
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for evaluation criteria
CREATE TABLE criteria (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    weight DECIMAL(3,2) NOT NULL DEFAULT 1.00,
    min_value INT NOT NULL DEFAULT 1,
    max_value INT NOT NULL DEFAULT 5,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for boarding houses (kost)
CREATE TABLE boarding_houses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    distance_to_campus DECIMAL(5,2), -- in kilometers
    monthly_price DECIMAL(10,2) NOT NULL,
    description TEXT,
    facilities JSON, -- Store facilities as JSON array
    contact_phone VARCHAR(20),
    contact_whatsapp VARCHAR(20),
    owner_name VARCHAR(100),
    images JSON, -- Store image paths as JSON array
    is_active BOOLEAN DEFAULT TRUE,
    total_score DECIMAL(5,2) DEFAULT 0,
    review_count INT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for boarding house scores per criteria
CREATE TABLE boarding_house_scores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    boarding_house_id INT NOT NULL,
    criteria_id INT NOT NULL,
    score INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (boarding_house_id) REFERENCES boarding_houses(id) ON DELETE CASCADE,
    FOREIGN KEY (criteria_id) REFERENCES criteria(id) ON DELETE CASCADE,
    UNIQUE KEY unique_house_criteria (boarding_house_id, criteria_id)
);

-- Table for student reviews
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    boarding_house_id INT NOT NULL,
    reviewer_name VARCHAR(100) NOT NULL,
    reviewer_email VARCHAR(100),
    student_id VARCHAR(20), -- NPM mahasiswa
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    lived_duration_months INT, -- How long they lived there
    is_verified BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (boarding_house_id) REFERENCES boarding_houses(id) ON DELETE CASCADE
);

-- Insert default criteria with weights
INSERT INTO criteria (name, description, weight, min_value, max_value) VALUES
('Harga Sewa', 'Harga sewa bulanan kost', 2.5, 1, 5),
('Jarak ke Kampus', 'Jarak dari kost ke Universitas Pamulang', 2.5, 1, 5),
('Keamanan', 'Tingkat keamanan lingkungan kost', 2.0, 1, 5),
('Fasilitas Kamar Mandi', 'Ketersediaan kamar mandi dalam/luar', 1.5, 1, 5),
('WiFi', 'Ketersediaan dan kualitas WiFi', 1.5, 1, 5),
('Fasilitas Laundry', 'Ketersediaan fasilitas laundry', 1.0, 1, 5),
('Dapur Bersama', 'Ketersediaan dapur untuk memasak', 1.0, 1, 5),
('Listrik', 'Stabilitas dan biaya listrik', 1.5, 1, 5),
('Kebersihan', 'Tingkat kebersihan kost dan lingkungan', 1.5, 1, 5),
('Lingkungan Tenang', 'Ketenangan lingkungan sekitar', 1.0, 1, 5),
('Lingkungan Ramah', 'Keramahan pemilik dan penghuni lain', 1.0, 1, 5);

-- Insert default admin user (password: admin123)
INSERT INTO admins (username, password, email) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- Create indexes for better performance
CREATE INDEX idx_boarding_houses_price ON boarding_houses(monthly_price);
CREATE INDEX idx_boarding_houses_distance ON boarding_houses(distance_to_campus);
CREATE INDEX idx_boarding_houses_score ON boarding_houses(total_score);
CREATE INDEX idx_boarding_houses_active ON boarding_houses(is_active);
CREATE INDEX idx_reviews_boarding_house ON reviews(boarding_house_id);
CREATE INDEX idx_reviews_approved ON reviews(is_approved);
