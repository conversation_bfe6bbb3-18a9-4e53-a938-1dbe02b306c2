<?php
/**
 * Boarding House Detail Page
 * Halaman detail kost dengan informasi lengkap dan review
 */

require_once 'config/config.php';
require_once 'classes/BoardingHouse.php';
require_once 'classes/ScoringSystem.php';
require_once 'classes/Review.php';

// Get boarding house ID
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$id) {
    redirect('index.php');
}

$boardingHouse = new BoardingHouse();
$scoring = new ScoringSystem();
$review = new Review();

// Get boarding house data
$house = $boardingHouse->getById($id);
if (!$house) {
    redirect('index.php');
}

// Get scores and reviews
$scores = $scoring->getScores($id);
$reviews = $review->getByBoardingHouse($id);
$review_stats = $review->getStatistics($id);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($house['name']); ?> - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-home"></i> KostEval
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-arrow-left"></i> Kembali ke Daftar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add-review.php?id=<?php echo $id; ?>">Beri Review</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Detail Header -->
    <div class="detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h1 class="display-6 fw-bold"><?php echo htmlspecialchars($house['name']); ?></h1>
                    <p class="lead">
                        <i class="fas fa-map-marker-alt"></i> 
                        <?php echo htmlspecialchars($house['address']); ?>
                    </p>
                    
                    <div class="row text-center mt-4">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="detail-score">
                                <div class="h2 text-primary mb-0"><?php echo number_format($house['total_score'], 1); ?></div>
                                <small class="text-muted">Skor Total</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="detail-score">
                                <div class="h4 text-success mb-0">Rp <?php echo number_format($house['monthly_price'], 0, ',', '.'); ?></div>
                                <small class="text-muted">per bulan</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="detail-score">
                                <div class="h4 text-info mb-0"><?php echo number_format($house['distance_to_campus'], 1); ?> km</div>
                                <small class="text-muted">ke kampus</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="detail-score">
                                <?php if ($house['avg_rating']): ?>
                                    <div class="h4 text-warning mb-0">
                                        <i class="fas fa-star"></i> <?php echo number_format($house['avg_rating'], 1); ?>
                                    </div>
                                    <small class="text-muted"><?php echo $house['review_count']; ?> review</small>
                                <?php else: ?>
                                    <div class="h6 text-muted mb-0">Belum ada review</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Images -->
                <?php 
                $images = json_decode($house['images'], true);
                if (!empty($images)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-images"></i> Foto Kost</h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="imageCarousel" class="carousel slide" data-bs-ride="carousel">
                            <div class="carousel-inner">
                                <?php foreach ($images as $index => $image): ?>
                                <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                                    <img src="<?php echo htmlspecialchars($image); ?>" class="d-block w-100" alt="Foto kost" style="height: 400px; object-fit: cover;">
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php if (count($images) > 1): ?>
                            <button class="carousel-control-prev" type="button" data-bs-target="#imageCarousel" data-bs-slide="prev">
                                <span class="carousel-control-prev-icon"></span>
                            </button>
                            <button class="carousel-control-next" type="button" data-bs-target="#imageCarousel" data-bs-slide="next">
                                <span class="carousel-control-next-icon"></span>
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Description -->
                <?php if ($house['description']): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> Deskripsi</h5>
                    </div>
                    <div class="card-body">
                        <p><?php echo nl2br(htmlspecialchars($house['description'])); ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Facilities -->
                <?php 
                $facilities = json_decode($house['facilities'], true);
                if (!empty($facilities)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list"></i> Fasilitas</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($facilities as $facility): ?>
                            <div class="col-md-6 col-sm-12 mb-2">
                                <i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($facility); ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Map -->
                <?php if ($house['latitude'] && $house['longitude']): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-map"></i> Lokasi</h5>
                    </div>
                    <div class="card-body p-0">
                        <img src="https://maps.googleapis.com/maps/api/staticmap?center=<?php echo $house['latitude']; ?>,<?php echo $house['longitude']; ?>&zoom=15&size=600x300&maptype=roadmap&markers=color:red%7C<?php echo $house['latitude']; ?>,<?php echo $house['longitude']; ?>&key=<?php echo GOOGLE_MAPS_API_KEY; ?>" 
                             alt="Peta lokasi" class="img-fluid w-100" style="height: 300px; object-fit: cover;">
                    </div>
                </div>
                <?php endif; ?>

                <!-- Reviews -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-comments"></i> Review (<?php echo count($reviews); ?>)</h5>
                        <a href="add-review.php?id=<?php echo $id; ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Tulis Review
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reviews)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-comment-slash fa-3x mb-3"></i>
                                <p>Belum ada review untuk kost ini.</p>
                                <a href="add-review.php?id=<?php echo $id; ?>" class="btn btn-primary">
                                    Jadilah yang pertama memberikan review
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($reviews as $rev): ?>
                            <div class="review-card card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($rev['reviewer_name']); ?></h6>
                                            <div class="review-rating mb-2">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $rev['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <small class="text-muted"><?php echo date('d M Y', strtotime($rev['created_at'])); ?></small>
                                    </div>
                                    
                                    <?php if ($rev['review_text']): ?>
                                    <p class="mb-2"><?php echo nl2br(htmlspecialchars($rev['review_text'])); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if ($rev['lived_duration_months']): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> Tinggal selama <?php echo $rev['lived_duration_months']; ?> bulan
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Contact Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-phone"></i> Kontak</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($house['owner_name']): ?>
                        <p><strong>Pemilik:</strong> <?php echo htmlspecialchars($house['owner_name']); ?></p>
                        <?php endif; ?>
                        
                        <?php if ($house['contact_phone']): ?>
                        <p>
                            <strong>Telepon:</strong><br>
                            <a href="tel:<?php echo $house['contact_phone']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-phone"></i> <?php echo htmlspecialchars($house['contact_phone']); ?>
                            </a>
                        </p>
                        <?php endif; ?>
                        
                        <?php if ($house['contact_whatsapp']): ?>
                        <p>
                            <strong>WhatsApp:</strong><br>
                            <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $house['contact_whatsapp']); ?>" 
                               target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp"></i> <?php echo htmlspecialchars($house['contact_whatsapp']); ?>
                            </a>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Detailed Scores -->
                <?php if (!empty($scores)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Skor Detail</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($scores as $score): ?>
                        <div class="criteria-score">
                            <div>
                                <strong><?php echo htmlspecialchars($score['criteria_name']); ?></strong>
                                <br><small class="text-muted">Bobot: <?php echo $score['weight']; ?></small>
                            </div>
                            <div class="text-end">
                                <div class="score-bar">
                                    <div class="score-fill" style="width: <?php echo ($score['score'] / 5) * 100; ?>%"></div>
                                </div>
                                <small><?php echo $score['score']; ?>/5</small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Review Statistics -->
                <?php if ($review_stats && $review_stats['total_reviews'] > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star"></i> Statistik Review</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="h3 text-warning"><?php echo number_format($review_stats['average_rating'], 1); ?></div>
                            <div class="review-rating mb-2">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?php echo $i <= $review_stats['average_rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <small class="text-muted"><?php echo $review_stats['total_reviews']; ?> review</small>
                        </div>
                        
                        <?php for ($i = 5; $i >= 1; $i--): ?>
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-2"><?php echo $i; ?> <i class="fas fa-star text-warning"></i></span>
                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                <?php 
                                $count_key = ['', 'one_star', 'two_star', 'three_star', 'four_star', 'five_star'][$i];
                                $percentage = $review_stats['total_reviews'] > 0 ? ($review_stats[$count_key] / $review_stats['total_reviews']) * 100 : 0;
                                ?>
                                <div class="progress-bar bg-warning" style="width: <?php echo $percentage; ?>%"></div>
                            </div>
                            <small class="text-muted"><?php echo $review_stats[$count_key]; ?></small>
                        </div>
                        <?php endfor; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="text-center">
                <small>&copy; 2024 <?php echo APP_NAME; ?>. Dikembangkan untuk Universitas Pamulang.</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
