# Testing Guide - Sistem Penilaian Kelayakan Kost

Panduan untuk testing semua fitur aplikasi sistem penilaian kelayakan kost.

## Persiapan Testing

### 1. Setup Database dengan Sample Data
```sql
-- Import schema utama
SOURCE database/schema.sql;

-- Import sample data untuk testing
SOURCE database/sample_data.sql;
```

### 2. Konfigurasi Testing
- Pastikan aplikasi berjalan di environment development
- Set Google Maps API key (opsional untuk testing maps)
- Pastikan folder `uploads/` memiliki permission write

## Test Cases

### A. Frontend User Interface Testing

#### 1. Halaman Utama (index.php)
**Test Case: Tampilan Daftar Kost**
- [ ] Halaman loading dengan benar
- [ ] Daftar kost ditampilkan dalam grid view
- [ ] Informasi kost (nama, alamat, harga, skor, rating) tampil lengkap
- [ ] Tombol "Lihat Detail" berfungsi
- [ ] Pagination berfungsi jika data > 12 item

**Test Case: Search & Filter**
- [ ] Search by nama kost berfungsi
- [ ] Search by al<PERSON><PERSON> berfung<PERSON>
- [ ] Filter harga min/max berfungsi
- [ ] Filter jarak maksimum berfungsi
- [ ] Filter skor minimum berfungsi
- [ ] Filter fasilitas (checkbox) berfungsi
- [ ] Sorting berdasarkan skor, harga, jarak, rating berfungsi
- [ ] Reset filter mengembalikan ke kondisi awal

**Test Case: Responsive Design**
- [ ] Tampilan mobile (< 768px) responsive
- [ ] Tampilan tablet (768px - 1024px) responsive
- [ ] Tampilan desktop (> 1024px) responsive
- [ ] Navigation menu mobile berfungsi

#### 2. Halaman Detail Kost (detail.php)
**Test Case: Informasi Detail**
- [ ] Informasi kost lengkap ditampilkan
- [ ] Skor total dan breakdown per kriteria tampil
- [ ] Rating dan jumlah review tampil
- [ ] Fasilitas ditampilkan dengan benar
- [ ] Kontak pemilik (telepon, WhatsApp) berfungsi

**Test Case: Maps Integration**
- [ ] Google Maps Static ditampilkan (jika API key tersedia)
- [ ] Lokasi marker sesuai dengan koordinat kost
- [ ] Fallback jika maps tidak tersedia

**Test Case: Review Section**
- [ ] Daftar review ditampilkan
- [ ] Rating bintang tampil dengan benar
- [ ] Statistik review (breakdown rating) akurat
- [ ] Link "Tulis Review" berfungsi

#### 3. Halaman Tambah Review (add-review.php)
**Test Case: Form Review**
- [ ] Form validation berfungsi (required fields)
- [ ] Rating bintang interaktif berfungsi
- [ ] Dropdown kost (jika tidak dari detail) berfungsi
- [ ] Submit review berhasil
- [ ] Redirect ke detail page setelah submit

**Test Case: Validation**
- [ ] Email validation berfungsi
- [ ] Rating 1-5 validation berfungsi
- [ ] Prevent duplicate review dari user yang sama
- [ ] Error message ditampilkan dengan jelas

### B. Admin Panel Testing

#### 1. Authentication (admin/login.php)
**Test Case: Login Process**
- [ ] Login dengan credentials benar berhasil
- [ ] Login dengan credentials salah ditolak
- [ ] Session management berfungsi
- [ ] Auto logout setelah timeout
- [ ] Redirect ke dashboard setelah login

#### 2. Dashboard (admin/dashboard.php)
**Test Case: Statistics Display**
- [ ] Total kost ditampilkan dengan benar
- [ ] Total review ditampilkan dengan benar
- [ ] Pending review count akurat
- [ ] Average score calculation benar
- [ ] Recent boarding houses list akurat
- [ ] Recent reviews list akurat

#### 3. Kelola Kost (admin/boarding-houses.php)
**Test Case: CRUD Operations**
- [ ] List kost ditampilkan dengan lengkap
- [ ] Add kost baru berhasil
- [ ] Edit kost existing berhasil
- [ ] Delete kost berhasil (soft delete)
- [ ] Form validation berfungsi

**Test Case: Scoring Integration**
- [ ] Link "Atur Skor" menuju halaman scoring
- [ ] Skor ditampilkan di list kost

#### 4. Atur Skor (admin/score-boarding-house.php)
**Test Case: Scoring System**
- [ ] Kriteria penilaian ditampilkan lengkap
- [ ] Range slider untuk setiap kriteria berfungsi
- [ ] Save scores berhasil
- [ ] Total score recalculation otomatis
- [ ] Existing scores loaded dengan benar

### C. Backend Logic Testing

#### 1. Database Operations
**Test Case: BoardingHouse Class**
```php
// Test create
$data = [
    'name' => 'Test Kost',
    'address' => 'Test Address',
    'monthly_price' => 1000000,
    // ... other fields
];
$result = $boardingHouse->create($data);
// Expected: return new ID

// Test getAll with filters
$filters = ['min_price' => 500000, 'max_price' => 1500000];
$houses = $boardingHouse->getAll($filters);
// Expected: filtered results

// Test getById
$house = $boardingHouse->getById(1);
// Expected: house data or false
```

**Test Case: ScoringSystem Class**
```php
// Test calculateTotalScore
$totalScore = $scoring->calculateTotalScore(1);
// Expected: calculated score based on criteria weights

// Test saveScores
$scores = [1 => 4, 2 => 5, 3 => 3]; // criteria_id => score
$result = $scoring->saveScores(1, $scores);
// Expected: true on success
```

**Test Case: Review Class**
```php
// Test create review
$reviewData = [
    'boarding_house_id' => 1,
    'reviewer_name' => 'Test User',
    'rating' => 4,
    // ... other fields
];
$result = $review->create($reviewData);
// Expected: new review ID

// Test duplicate prevention
$duplicate = $review->hasUserReviewed(1, '<EMAIL>');
// Expected: true if already reviewed
```

#### 2. Scoring Algorithm Testing
**Test Case: Weighted Scoring**
```
Given:
- Harga Sewa: score 4, weight 2.5
- Jarak ke Kampus: score 5, weight 2.5
- Keamanan: score 3, weight 2.0

Expected Calculation:
Base Score = ((4*2.5) + (5*2.5) + (3*2.0)) / (2.5+2.5+2.0) * 20
           = (10 + 12.5 + 6) / 7 * 20
           = 28.5 / 7 * 20
           = 81.43

With Review (avg 4.5):
Review Score = 4.5/5 * 100 = 90
Final Score = (81.43 * 0.7) + (90 * 0.3) = 84.0
```

### D. Integration Testing

#### 1. End-to-End User Flow
**Test Case: Complete User Journey**
1. [ ] User visits homepage
2. [ ] User searches for kost with filters
3. [ ] User clicks on kost detail
4. [ ] User views complete information
5. [ ] User submits review
6. [ ] Review appears in kost detail
7. [ ] Kost score updated automatically

**Test Case: Admin Management Flow**
1. [ ] Admin logs in
2. [ ] Admin adds new kost
3. [ ] Admin sets scores for criteria
4. [ ] Admin approves pending reviews
5. [ ] Changes reflected on frontend

#### 2. Data Consistency Testing
**Test Case: Score Recalculation**
- [ ] Adding new review updates average rating
- [ ] Changing criteria scores updates total score
- [ ] Deleting review recalculates statistics
- [ ] All calculations remain consistent

### E. Performance Testing

#### 1. Load Testing
**Test Case: Database Performance**
- [ ] Homepage loads < 2 seconds with 100+ kost
- [ ] Search/filter response < 1 second
- [ ] Detail page loads < 1.5 seconds
- [ ] Admin operations complete < 3 seconds

#### 2. Scalability Testing
**Test Case: Large Dataset**
- [ ] Performance with 1000+ boarding houses
- [ ] Performance with 10000+ reviews
- [ ] Pagination efficiency
- [ ] Search index performance

### F. Security Testing

#### 1. Input Validation
**Test Case: XSS Prevention**
- [ ] HTML tags in form inputs are escaped
- [ ] JavaScript injection prevented
- [ ] SQL injection prevented

**Test Case: Authentication Security**
- [ ] Admin pages require authentication
- [ ] Session timeout works correctly
- [ ] Password hashing secure

#### 2. Data Protection
**Test Case: Sensitive Data**
- [ ] Database credentials not exposed
- [ ] Error messages don't reveal system info
- [ ] File upload restrictions work

## Automated Testing

### Unit Tests (Recommended)
```php
// Example PHPUnit test
class BoardingHouseTest extends PHPUnit\Framework\TestCase {
    public function testCreateBoardingHouse() {
        $bh = new BoardingHouse();
        $data = ['name' => 'Test', 'address' => 'Test', 'monthly_price' => 1000000];
        $result = $bh->create($data);
        $this->assertIsInt($result);
    }
}
```

### Browser Testing (Selenium)
```javascript
// Example Selenium test
describe('Kost Search', function() {
    it('should filter by price range', function() {
        browser.url('/');
        browser.setValue('input[name="min_price"]', '500000');
        browser.setValue('input[name="max_price"]', '1000000');
        browser.click('button[type="submit"]');
        // Assert results are within price range
    });
});
```

## Bug Reporting Template

```
**Bug Title:** [Brief description]

**Environment:**
- Browser: [Chrome/Firefox/Safari/etc.]
- OS: [Windows/Mac/Linux]
- Screen Resolution: [1920x1080/etc.]

**Steps to Reproduce:**
1. Go to [URL]
2. Click on [element]
3. Enter [data]
4. Click [button]

**Expected Result:**
[What should happen]

**Actual Result:**
[What actually happened]

**Screenshots:**
[Attach if applicable]

**Priority:** [High/Medium/Low]
```

## Test Completion Checklist

- [ ] All frontend pages tested
- [ ] All admin functions tested
- [ ] Database operations verified
- [ ] Scoring algorithm validated
- [ ] Security measures confirmed
- [ ] Performance benchmarks met
- [ ] Cross-browser compatibility checked
- [ ] Mobile responsiveness verified
- [ ] Error handling tested
- [ ] Documentation updated
