<?php
/**
 * Admin Dashboard
 * Dashboard utama untuk admin
 */

require_once '../config/config.php';
require_once '../classes/BoardingHouse.php';
require_once '../classes/Review.php';
require_once '../classes/ScoringSystem.php';

// Require admin login
require_admin_login();

$boardingHouse = new BoardingHouse();
$review = new Review();
$scoring = new ScoringSystem();

// Get statistics
try {
    $db = getDB();
    
    // Total boarding houses
    $stmt = $db->query("SELECT COUNT(*) as total FROM boarding_houses WHERE is_active = 1");
    $total_houses = $stmt->fetch()['total'];
    
    // Total reviews
    $stmt = $db->query("SELECT COUNT(*) as total FROM reviews WHERE is_approved = 1");
    $total_reviews = $stmt->fetch()['total'];
    
    // Pending reviews
    $stmt = $db->query("SELECT COUNT(*) as total FROM reviews WHERE is_approved = 0");
    $pending_reviews = $stmt->fetch()['total'];
    
    // Average score
    $stmt = $db->query("SELECT AVG(total_score) as avg_score FROM boarding_houses WHERE is_active = 1 AND total_score > 0");
    $avg_score = $stmt->fetch()['avg_score'] ?? 0;
    
    // Recent boarding houses
    $recent_houses = $boardingHouse->getAll([], 'created_at', 'DESC', 5);
    
    // Recent reviews
    $recent_reviews = $review->getAll(5);
    
} catch (PDOException $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $total_houses = $total_reviews = $pending_reviews = $avg_score = 0;
    $recent_houses = $recent_reviews = [];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-tachometer-alt"></i> Admin Panel
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Lihat Website
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="boarding-houses.php">
                                <i class="fas fa-home"></i> Kelola Kost
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="criteria.php">
                                <i class="fas fa-list-check"></i> Kriteria Penilaian
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reviews.php">
                                <i class="fas fa-comments"></i> Kelola Review
                                <?php if ($pending_reviews > 0): ?>
                                <span class="badge bg-danger"><?php echo $pending_reviews; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i> Laporan
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="boarding-houses.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> Tambah Kost
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total Kost
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_houses); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-home fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Total Review
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_reviews); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Review Pending
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($pending_reviews); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Rata-rata Skor
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($avg_score, 1); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <!-- Recent Boarding Houses -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Kost Terbaru</h6>
                                <a href="boarding-houses.php" class="btn btn-sm btn-primary">Lihat Semua</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_houses)): ?>
                                    <div class="text-center text-muted py-3">
                                        <i class="fas fa-home fa-3x mb-3"></i>
                                        <p>Belum ada data kost</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_houses as $house): ?>
                                    <div class="d-flex align-items-center py-2 border-bottom">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($house['name']); ?></h6>
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt"></i> 
                                                <?php echo htmlspecialchars(substr($house['address'], 0, 50)); ?>...
                                            </small>
                                            <br>
                                            <small class="text-success">
                                                Rp <?php echo number_format($house['monthly_price'], 0, ',', '.'); ?>/bulan
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="badge bg-primary"><?php echo number_format($house['total_score'], 1); ?></div>
                                            <br>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($house['created_at'])); ?></small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Reviews -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Review Terbaru</h6>
                                <a href="reviews.php" class="btn btn-sm btn-primary">Lihat Semua</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_reviews)): ?>
                                    <div class="text-center text-muted py-3">
                                        <i class="fas fa-comments fa-3x mb-3"></i>
                                        <p>Belum ada review</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_reviews as $rev): ?>
                                    <div class="d-flex align-items-center py-2 border-bottom">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($rev['reviewer_name']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($rev['boarding_house_name']); ?>
                                            </small>
                                            <br>
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $rev['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <?php if (!$rev['is_approved']): ?>
                                                <span class="badge bg-warning">Pending</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php endif; ?>
                                            <br>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($rev['created_at'])); ?></small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
