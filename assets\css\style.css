/**
 * Custom CSS for Boarding House Evaluation System
 * Sistem Penilaian <PERSON> Kost - Universitas Pamulang
 */

/* Global Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-section h1 {
    color: white !important;
}

/* Card Styles */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    border-radius: 10px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.boarding-house-card .card-img-top {
    border-radius: 10px 10px 0 0;
    transition: transform 0.3s ease;
}

.boarding-house-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Score Badge */
.score-badge {
    border-radius: 10px !important;
    font-size: 0.9rem;
}

/* Stars Rating */
.stars {
    font-size: 0.9rem;
}

.stars .fa-star {
    margin-right: 2px;
}

/* Facility Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.6em;
}

/* Filter Section */
.card-header {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
}

/* Form Styles */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .score-badge {
        font-size: 0.8rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .boarding-house-card .card-img-top {
        height: 180px;
    }
}

/* Detail Page Styles */
.detail-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
}

.detail-score {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.criteria-score {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.criteria-score:last-child {
    border-bottom: none;
}

.score-bar {
    width: 100px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #198754 100%);
    transition: width 0.3s ease;
}

/* Review Styles */
.review-card {
    border-left: 4px solid var(--primary-color);
    margin-bottom: 1rem;
}

.review-rating {
    color: var(--warning-color);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Admin Panel Styles */
.admin-sidebar {
    background: var(--dark-color);
    min-height: calc(100vh - 56px);
}

.admin-sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1rem;
    border-radius: 5px;
    margin: 0.2rem 0;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background: var(--primary-color);
    color: white;
}

/* Table Styles */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.table th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-hover {
    transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
}

/* Print Styles */
@media print {
    .navbar, .btn, .pagination, footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
