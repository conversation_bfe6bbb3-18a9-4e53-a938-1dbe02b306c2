<?php
/**
 * Review Class
 * Kelas untuk mengelola review dan rating kost
 */

require_once '../config/config.php';

class Review {
    private $conn;
    
    public function __construct() {
        $this->conn = getDB();
    }
    
    // Create new review
    public function create($data) {
        try {
            $sql = "INSERT INTO reviews (boarding_house_id, reviewer_name, reviewer_email, student_id, 
                    rating, review_text, lived_duration_months) 
                    VALUES (:boarding_house_id, :reviewer_name, :reviewer_email, :student_id, 
                    :rating, :review_text, :lived_duration_months)";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $data['boarding_house_id']);
            $stmt->bindParam(':reviewer_name', $data['reviewer_name']);
            $stmt->bindParam(':reviewer_email', $data['reviewer_email']);
            $stmt->bindParam(':student_id', $data['student_id']);
            $stmt->bindParam(':rating', $data['rating']);
            $stmt->bindParam(':review_text', $data['review_text']);
            $stmt->bindParam(':lived_duration_months', $data['lived_duration_months']);
            
            if ($stmt->execute()) {
                $review_id = $this->conn->lastInsertId();
                
                // Update boarding house average rating and review count
                $this->updateBoardingHouseRating($data['boarding_house_id']);
                
                // Recalculate total score including review impact
                $scoring = new ScoringSystem();
                $total_score = $scoring->calculateTotalScore($data['boarding_house_id']);
                $scoring->updateTotalScore($data['boarding_house_id'], $total_score);
                
                return $review_id;
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error creating review: " . $e->getMessage());
            return false;
        }
    }
    
    // Get reviews for a boarding house
    public function getByBoardingHouse($boarding_house_id, $approved_only = true) {
        try {
            $sql = "SELECT * FROM reviews WHERE boarding_house_id = :boarding_house_id";
            
            if ($approved_only) {
                $sql .= " AND is_approved = 1";
            }
            
            $sql .= " ORDER BY created_at DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting reviews: " . $e->getMessage());
            return [];
        }
    }
    
    // Get all reviews (for admin)
    public function getAll($limit = null, $offset = 0) {
        try {
            $sql = "SELECT r.*, bh.name as boarding_house_name 
                    FROM reviews r
                    JOIN boarding_houses bh ON r.boarding_house_id = bh.id
                    ORDER BY r.created_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT :limit OFFSET :offset";
            }
            
            $stmt = $this->conn->prepare($sql);
            
            if ($limit) {
                $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
                $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting all reviews: " . $e->getMessage());
            return [];
        }
    }
    
    // Approve/disapprove review
    public function updateApprovalStatus($review_id, $is_approved) {
        try {
            $sql = "UPDATE reviews SET is_approved = :is_approved WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':is_approved', $is_approved, PDO::PARAM_BOOL);
            $stmt->bindParam(':id', $review_id);
            
            if ($stmt->execute()) {
                // Get boarding house ID to update rating
                $sql = "SELECT boarding_house_id FROM reviews WHERE id = :id";
                $stmt = $this->conn->prepare($sql);
                $stmt->bindParam(':id', $review_id);
                $stmt->execute();
                $review = $stmt->fetch();
                
                if ($review) {
                    $this->updateBoardingHouseRating($review['boarding_house_id']);
                    
                    // Recalculate total score
                    $scoring = new ScoringSystem();
                    $total_score = $scoring->calculateTotalScore($review['boarding_house_id']);
                    $scoring->updateTotalScore($review['boarding_house_id'], $total_score);
                }
                
                return true;
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error updating review approval: " . $e->getMessage());
            return false;
        }
    }
    
    // Delete review
    public function delete($review_id) {
        try {
            // Get boarding house ID before deletion
            $sql = "SELECT boarding_house_id FROM reviews WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $review_id);
            $stmt->execute();
            $review = $stmt->fetch();
            
            // Delete review
            $sql = "DELETE FROM reviews WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $review_id);
            
            if ($stmt->execute() && $review) {
                // Update boarding house rating
                $this->updateBoardingHouseRating($review['boarding_house_id']);
                
                // Recalculate total score
                $scoring = new ScoringSystem();
                $total_score = $scoring->calculateTotalScore($review['boarding_house_id']);
                $scoring->updateTotalScore($review['boarding_house_id'], $total_score);
                
                return true;
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error deleting review: " . $e->getMessage());
            return false;
        }
    }
    
    // Update boarding house average rating and review count
    private function updateBoardingHouseRating($boarding_house_id) {
        try {
            $sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as review_count 
                    FROM reviews 
                    WHERE boarding_house_id = :boarding_house_id AND is_approved = 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            $result = $stmt->fetch();
            
            $avg_rating = $result['avg_rating'] ? round($result['avg_rating'], 2) : 0;
            $review_count = $result['review_count'];
            
            $sql = "UPDATE boarding_houses 
                    SET average_rating = :avg_rating, review_count = :review_count 
                    WHERE id = :id";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':avg_rating', $avg_rating);
            $stmt->bindParam(':review_count', $review_count);
            $stmt->bindParam(':id', $boarding_house_id);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating boarding house rating: " . $e->getMessage());
            return false;
        }
    }
    
    // Check if user already reviewed a boarding house
    public function hasUserReviewed($boarding_house_id, $student_id = null, $email = null) {
        try {
            $sql = "SELECT COUNT(*) as count FROM reviews WHERE boarding_house_id = :boarding_house_id";
            $params = [':boarding_house_id' => $boarding_house_id];
            
            if ($student_id) {
                $sql .= " AND student_id = :student_id";
                $params[':student_id'] = $student_id;
            } elseif ($email) {
                $sql .= " AND reviewer_email = :email";
                $params[':email'] = $email;
            }
            
            $stmt = $this->conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            
            $result = $stmt->fetch();
            return $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking user review: " . $e->getMessage());
            return false;
        }
    }
    
    // Get review statistics
    public function getStatistics($boarding_house_id) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_reviews,
                        AVG(rating) as average_rating,
                        COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
                        COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
                        COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
                        COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
                        COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star
                    FROM reviews 
                    WHERE boarding_house_id = :boarding_house_id AND is_approved = 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting review statistics: " . $e->getMessage());
            return null;
        }
    }
}
?>
