<?php
/**
 * ScoringSystem Class
 * Kelas untuk mengelola sistem penilaian dan perhitungan skor
 */

require_once '../config/config.php';

class ScoringSystem {
    private $conn;
    
    public function __construct() {
        $this->conn = getDB();
    }
    
    // Get all criteria
    public function getCriteria() {
        try {
            $sql = "SELECT * FROM criteria WHERE is_active = 1 ORDER BY weight DESC, name ASC";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting criteria: " . $e->getMessage());
            return [];
        }
    }
    
    // Save scores for a boarding house
    public function saveScores($boarding_house_id, $scores) {
        try {
            $this->conn->beginTransaction();
            
            // Delete existing scores
            $sql = "DELETE FROM boarding_house_scores WHERE boarding_house_id = :boarding_house_id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            // Insert new scores
            $sql = "INSERT INTO boarding_house_scores (boarding_house_id, criteria_id, score) 
                    VALUES (:boarding_house_id, :criteria_id, :score)";
            $stmt = $this->conn->prepare($sql);
            
            foreach ($scores as $criteria_id => $score) {
                $stmt->bindParam(':boarding_house_id', $boarding_house_id);
                $stmt->bindParam(':criteria_id', $criteria_id);
                $stmt->bindParam(':score', $score);
                $stmt->execute();
            }
            
            // Calculate and update total score
            $total_score = $this->calculateTotalScore($boarding_house_id);
            $this->updateTotalScore($boarding_house_id, $total_score);
            
            $this->conn->commit();
            return true;
        } catch (PDOException $e) {
            $this->conn->rollBack();
            error_log("Error saving scores: " . $e->getMessage());
            return false;
        }
    }
    
    // Calculate total score for a boarding house
    public function calculateTotalScore($boarding_house_id) {
        try {
            $sql = "SELECT SUM(bhs.score * c.weight) as weighted_score, SUM(c.weight) as total_weight
                    FROM boarding_house_scores bhs
                    JOIN criteria c ON bhs.criteria_id = c.id
                    WHERE bhs.boarding_house_id = :boarding_house_id AND c.is_active = 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            $result = $stmt->fetch();
            
            if ($result && $result['total_weight'] > 0) {
                // Normalize score to 0-100 scale
                $base_score = ($result['weighted_score'] / $result['total_weight']) * 20; // Convert 1-5 scale to 0-100
                
                // Get average review rating and incorporate it
                $review_score = $this->getAverageReviewScore($boarding_house_id);
                
                if ($review_score > 0) {
                    // Combine base score with review score
                    $final_score = ($base_score * (1 - REVIEW_WEIGHT)) + ($review_score * REVIEW_WEIGHT);
                } else {
                    $final_score = $base_score;
                }
                
                return round($final_score, 2);
            }
            
            return 0;
        } catch (PDOException $e) {
            error_log("Error calculating total score: " . $e->getMessage());
            return 0;
        }
    }
    
    // Get average review score (converted to 0-100 scale)
    private function getAverageReviewScore($boarding_house_id) {
        try {
            $sql = "SELECT AVG(rating) as avg_rating FROM reviews 
                    WHERE boarding_house_id = :boarding_house_id AND is_approved = 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            $result = $stmt->fetch();
            
            if ($result && $result['avg_rating']) {
                // Convert 1-5 scale to 0-100 scale
                return ($result['avg_rating'] / 5) * 100;
            }
            
            return 0;
        } catch (PDOException $e) {
            error_log("Error getting average review score: " . $e->getMessage());
            return 0;
        }
    }
    
    // Update total score in boarding house table
    private function updateTotalScore($boarding_house_id, $total_score) {
        try {
            $sql = "UPDATE boarding_houses SET total_score = :total_score WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':total_score', $total_score);
            $stmt->bindParam(':id', $boarding_house_id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating total score: " . $e->getMessage());
            return false;
        }
    }
    
    // Get scores for a boarding house
    public function getScores($boarding_house_id) {
        try {
            $sql = "SELECT bhs.*, c.name as criteria_name, c.weight 
                    FROM boarding_house_scores bhs
                    JOIN criteria c ON bhs.criteria_id = c.id
                    WHERE bhs.boarding_house_id = :boarding_house_id
                    ORDER BY c.weight DESC, c.name ASC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':boarding_house_id', $boarding_house_id);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting scores: " . $e->getMessage());
            return [];
        }
    }
    
    // Update criteria weight
    public function updateCriteriaWeight($criteria_id, $weight) {
        try {
            $sql = "UPDATE criteria SET weight = :weight WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':weight', $weight);
            $stmt->bindParam(':id', $criteria_id);
            
            if ($stmt->execute()) {
                // Recalculate all boarding house scores
                $this->recalculateAllScores();
                return true;
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error updating criteria weight: " . $e->getMessage());
            return false;
        }
    }
    
    // Recalculate all boarding house scores
    public function recalculateAllScores() {
        try {
            $sql = "SELECT id FROM boarding_houses WHERE is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $boarding_houses = $stmt->fetchAll();
            
            foreach ($boarding_houses as $house) {
                $total_score = $this->calculateTotalScore($house['id']);
                $this->updateTotalScore($house['id'], $total_score);
            }
            
            return true;
        } catch (PDOException $e) {
            error_log("Error recalculating all scores: " . $e->getMessage());
            return false;
        }
    }
}
?>
