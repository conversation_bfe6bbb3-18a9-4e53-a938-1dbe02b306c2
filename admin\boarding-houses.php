<?php
/**
 * Admin Boarding Houses Management
 * Halaman untuk mengelola data kost
 */

require_once '../config/config.php';
require_once '../classes/BoardingHouse.php';
require_once '../classes/ScoringSystem.php';

// Require admin login
require_admin_login();

$boardingHouse = new BoardingHouse();
$scoring = new ScoringSystem();

$message = '';
$message_type = '';

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $data = [
            'name' => sanitize_input($_POST['name']),
            'address' => sanitize_input($_POST['address']),
            'latitude' => !empty($_POST['latitude']) ? (float)$_POST['latitude'] : null,
            'longitude' => !empty($_POST['longitude']) ? (float)$_POST['longitude'] : null,
            'monthly_price' => (float)$_POST['monthly_price'],
            'description' => sanitize_input($_POST['description']),
            'facilities' => $_POST['facilities'] ?? [],
            'contact_phone' => sanitize_input($_POST['contact_phone']),
            'contact_whatsapp' => sanitize_input($_POST['contact_whatsapp']),
            'owner_name' => sanitize_input($_POST['owner_name']),
            'images' => [] // TODO: Handle image upload
        ];
        
        if ($action === 'add') {
            $result = $boardingHouse->create($data);
            if ($result) {
                $message = 'Kost berhasil ditambahkan!';
                $message_type = 'success';
                $action = 'list';
            } else {
                $message = 'Gagal menambahkan kost.';
                $message_type = 'danger';
            }
        } else {
            $result = $boardingHouse->update($id, $data);
            if ($result) {
                $message = 'Kost berhasil diperbarui!';
                $message_type = 'success';
                $action = 'list';
            } else {
                $message = 'Gagal memperbarui kost.';
                $message_type = 'danger';
            }
        }
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    $result = $boardingHouse->delete($id);
    if ($result) {
        $message = 'Kost berhasil dihapus!';
        $message_type = 'success';
    } else {
        $message = 'Gagal menghapus kost.';
        $message_type = 'danger';
    }
    $action = 'list';
}

// Get data for edit
$edit_data = null;
if ($action === 'edit' && $id) {
    $edit_data = $boardingHouse->getById($id);
    if (!$edit_data) {
        $action = 'list';
        $message = 'Data kost tidak ditemukan.';
        $message_type = 'danger';
    }
}

// Get boarding houses list
$boarding_houses = [];
if ($action === 'list') {
    $boarding_houses = $boardingHouse->getAll([], 'created_at', 'DESC');
}

// Get criteria for scoring
$criteria = $scoring->getCriteria();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Kost - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-tachometer-alt"></i> Admin Panel
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Lihat Website
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="boarding-houses.php">
                                <i class="fas fa-home"></i> Kelola Kost
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="criteria.php">
                                <i class="fas fa-list-check"></i> Kriteria Penilaian
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reviews.php">
                                <i class="fas fa-comments"></i> Kelola Review
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i> Laporan
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php if ($action === 'add'): ?>
                            Tambah Kost Baru
                        <?php elseif ($action === 'edit'): ?>
                            Edit Kost
                        <?php else: ?>
                            Kelola Kost
                        <?php endif; ?>
                    </h1>
                    
                    <?php if ($action === 'list'): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> Tambah Kost
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Boarding Houses List -->
                    <div class="card shadow">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nama Kost</th>
                                            <th>Alamat</th>
                                            <th>Harga/Bulan</th>
                                            <th>Jarak (km)</th>
                                            <th>Skor</th>
                                            <th>Review</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($boarding_houses)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center text-muted py-4">
                                                <i class="fas fa-home fa-3x mb-3"></i>
                                                <p>Belum ada data kost</p>
                                                <a href="?action=add" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> Tambah Kost Pertama
                                                </a>
                                            </td>
                                        </tr>
                                        <?php else: ?>
                                            <?php foreach ($boarding_houses as $house): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($house['name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($house['owner_name']); ?></small>
                                                </td>
                                                <td>
                                                    <small><?php echo htmlspecialchars(substr($house['address'], 0, 50)); ?>...</small>
                                                </td>
                                                <td>
                                                    <span class="text-success fw-bold">
                                                        Rp <?php echo number_format($house['monthly_price'], 0, ',', '.'); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo $house['distance_to_campus'] ? number_format($house['distance_to_campus'], 1) : '-'; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo number_format($house['total_score'], 1); ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($house['review_count'] > 0): ?>
                                                        <i class="fas fa-star text-warning"></i> 
                                                        <?php echo number_format($house['avg_rating'], 1); ?> 
                                                        (<?php echo $house['review_count']; ?>)
                                                    <?php else: ?>
                                                        <span class="text-muted">Belum ada</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="../detail.php?id=<?php echo $house['id']; ?>" 
                                                           class="btn btn-outline-info" target="_blank" title="Lihat Detail">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="?action=edit&id=<?php echo $house['id']; ?>" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="score-boarding-house.php?id=<?php echo $house['id']; ?>"
                                                           class="btn btn-outline-success" title="Atur Skor">
                                                            <i class="fas fa-star"></i>
                                                        </a>
                                                        <a href="?action=delete&id=<?php echo $house['id']; ?>" 
                                                           class="btn btn-outline-danger btn-delete" title="Hapus">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Form -->
                    <div class="card shadow">
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-8">
                                        <!-- Basic Information -->
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Nama Kost <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="name" id="name"
                                                   value="<?php echo htmlspecialchars($edit_data['name'] ?? ''); ?>" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="address" class="form-label">Alamat Lengkap <span class="text-danger">*</span></label>
                                            <textarea class="form-control" name="address" id="address" rows="3" required><?php echo htmlspecialchars($edit_data['address'] ?? ''); ?></textarea>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="latitude" class="form-label">Latitude</label>
                                                    <input type="number" step="any" class="form-control" name="latitude" id="latitude"
                                                           value="<?php echo $edit_data['latitude'] ?? ''; ?>" placeholder="-6.3373">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="longitude" class="form-label">Longitude</label>
                                                    <input type="number" step="any" class="form-control" name="longitude" id="longitude"
                                                           value="<?php echo $edit_data['longitude'] ?? ''; ?>" placeholder="106.7537">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="monthly_price" class="form-label">Harga Sewa per Bulan <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">Rp</span>
                                                <input type="number" class="form-control" name="monthly_price" id="monthly_price"
                                                       value="<?php echo $edit_data['monthly_price'] ?? ''; ?>" required>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Deskripsi</label>
                                            <textarea class="form-control" name="description" id="description" rows="4"><?php echo htmlspecialchars($edit_data['description'] ?? ''); ?></textarea>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <!-- Contact Information -->
                                        <div class="mb-3">
                                            <label for="owner_name" class="form-label">Nama Pemilik</label>
                                            <input type="text" class="form-control" name="owner_name" id="owner_name"
                                                   value="<?php echo htmlspecialchars($edit_data['owner_name'] ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="contact_phone" class="form-label">Nomor Telepon</label>
                                            <input type="text" class="form-control" name="contact_phone" id="contact_phone"
                                                   value="<?php echo htmlspecialchars($edit_data['contact_phone'] ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="contact_whatsapp" class="form-label">WhatsApp</label>
                                            <input type="text" class="form-control" name="contact_whatsapp" id="contact_whatsapp"
                                                   value="<?php echo htmlspecialchars($edit_data['contact_whatsapp'] ?? ''); ?>">
                                        </div>

                                        <!-- Facilities -->
                                        <div class="mb-3">
                                            <label class="form-label">Fasilitas</label>
                                            <?php
                                            $facilities_options = ['WiFi', 'Kamar Mandi Dalam', 'Kamar Mandi Luar', 'Laundry', 'Dapur', 'AC', 'Kipas Angin', 'Parkir Motor', 'Parkir Mobil', 'CCTV', 'Satpam'];
                                            $selected_facilities = $edit_data ? json_decode($edit_data['facilities'], true) : [];
                                            ?>
                                            <div class="row">
                                                <?php foreach ($facilities_options as $facility): ?>
                                                <div class="col-12 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="facilities[]"
                                                               value="<?php echo $facility; ?>" id="facility_<?php echo str_replace(' ', '_', $facility); ?>"
                                                               <?php echo in_array($facility, $selected_facilities) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="facility_<?php echo str_replace(' ', '_', $facility); ?>">
                                                            <?php echo $facility; ?>
                                                        </label>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <div class="d-flex justify-content-between">
                                    <a href="boarding-houses.php" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Kembali
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?php echo $action === 'add' ? 'Tambah' : 'Perbarui'; ?> Kost
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
