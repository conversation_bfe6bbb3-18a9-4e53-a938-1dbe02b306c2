/**
 * Main JavaScript for Boarding House Evaluation System
 * Sistem Penilaian Kelayakan Kost - Universitas Pamulang
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Grid/List view toggle
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const container = document.getElementById('boarding-houses-container');

    if (gridViewBtn && listViewBtn && container) {
        gridViewBtn.addEventListener('click', function() {
            container.className = 'row';
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            
            // Update card classes for grid view
            const cards = container.querySelectorAll('.boarding-house-card');
            cards.forEach(card => {
                card.className = 'col-lg-4 col-md-6 mb-4 boarding-house-card';
            });
        });

        listViewBtn.addEventListener('click', function() {
            container.className = 'row';
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
            
            // Update card classes for list view
            const cards = container.querySelectorAll('.boarding-house-card');
            cards.forEach(card => {
                card.className = 'col-12 mb-3 boarding-house-card';
            });
        });
    }

    // Price range formatting
    const priceInputs = document.querySelectorAll('input[name="min_price"], input[name="max_price"]');
    priceInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value) {
                this.value = parseInt(this.value).toLocaleString('id-ID');
            }
        });
        
        input.addEventListener('focus', function() {
            this.value = this.value.replace(/\./g, '');
        });
    });

    // Auto-submit form on sort change
    const sortSelect = document.querySelector('select[name="sort"]');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            // Update order based on sort selection
            const orderInput = document.querySelector('input[name="order"]');
            if (orderInput) {
                if (this.value === 'monthly_price' || this.value === 'distance_to_campus') {
                    orderInput.value = 'ASC';
                } else {
                    orderInput.value = 'DESC';
                }
            }
            
            // Auto-submit form
            this.closest('form').submit();
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Image lazy loading
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Rating stars interaction
    const ratingStars = document.querySelectorAll('.rating-stars .fa-star');
    ratingStars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            const ratingInput = this.closest('.rating-container').querySelector('input[name="rating"]');
            if (ratingInput) {
                ratingInput.value = rating;
            }
            
            // Update visual stars
            const allStars = this.closest('.rating-stars').querySelectorAll('.fa-star');
            allStars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.remove('text-muted');
                    s.classList.add('text-warning');
                } else {
                    s.classList.remove('text-warning');
                    s.classList.add('text-muted');
                }
            });
        });
        
        star.addEventListener('mouseenter', function() {
            const hoverRating = index + 1;
            const allStars = this.closest('.rating-stars').querySelectorAll('.fa-star');
            allStars.forEach((s, i) => {
                if (i < hoverRating) {
                    s.classList.add('text-warning');
                    s.classList.remove('text-muted');
                } else {
                    s.classList.add('text-muted');
                    s.classList.remove('text-warning');
                }
            });
        });
    });

    // Reset rating stars on mouse leave
    const ratingContainers = document.querySelectorAll('.rating-stars');
    ratingContainers.forEach(container => {
        container.addEventListener('mouseleave', function() {
            const ratingInput = this.closest('.rating-container').querySelector('input[name="rating"]');
            const currentRating = ratingInput ? parseInt(ratingInput.value) : 0;
            
            const allStars = this.querySelectorAll('.fa-star');
            allStars.forEach((s, i) => {
                if (i < currentRating) {
                    s.classList.add('text-warning');
                    s.classList.remove('text-muted');
                } else {
                    s.classList.add('text-muted');
                    s.classList.remove('text-warning');
                }
            });
        });
    });

    // Auto-hide alerts
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease';
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    });

    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Apakah Anda yakin ingin menghapus item ini?')) {
                e.preventDefault();
            }
        });
    });

    // Number formatting for Indonesian locale
    const numberInputs = document.querySelectorAll('input[type="number"].format-number');
    numberInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value) {
                const number = parseFloat(this.value);
                this.setAttribute('data-value', number);
                this.value = number.toLocaleString('id-ID');
            }
        });
        
        input.addEventListener('focus', function() {
            const dataValue = this.getAttribute('data-value');
            if (dataValue) {
                this.value = dataValue;
            }
        });
    });

    // Mobile menu improvements
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                if (navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }
            }
        });
    }

    // Scroll to top button
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    scrollToTopBtn.className = 'btn btn-primary position-fixed';
    scrollToTopBtn.style.cssText = `
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    
    document.body.appendChild(scrollToTopBtn);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.style.display = 'block';
        } else {
            scrollToTopBtn.style.display = 'none';
        }
    });
    
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatDistance(distance) {
    if (distance < 1) {
        return (distance * 1000).toFixed(0) + ' m';
    }
    return distance.toFixed(1) + ' km';
}

function showLoading(element) {
    element.innerHTML = '<span class="loading"></span> Memuat...';
    element.disabled = true;
}

function hideLoading(element, originalText) {
    element.innerHTML = originalText;
    element.disabled = false;
}

// AJAX helper function
function makeRequest(url, method = 'GET', data = null) {
    return fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: data ? JSON.stringify(data) : null
    })
    .then(response => response.json())
    .catch(error => {
        console.error('Request failed:', error);
        throw error;
    });
}
