<?php
/**
 * Admin Boarding House Scoring
 * Halaman untuk mengatur skor kost berdasarkan kriteria
 */

require_once '../config/config.php';
require_once '../classes/BoardingHouse.php';
require_once '../classes/ScoringSystem.php';

// Require admin login
require_admin_login();

$boardingHouse = new BoardingHouse();
$scoring = new ScoringSystem();

$message = '';
$message_type = '';

// Get boarding house ID
$id = $_GET['id'] ?? 0;
if (!$id) {
    redirect('admin/boarding-houses.php');
}

// Get boarding house data
$house = $boardingHouse->getById($id);
if (!$house) {
    redirect('admin/boarding-houses.php');
}

// Get criteria
$criteria = $scoring->getCriteria();

// Get existing scores
$existing_scores = $scoring->getScores($id);
$score_values = [];
foreach ($existing_scores as $score) {
    $score_values[$score['criteria_id']] = $score['score'];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $scores = [];
    foreach ($criteria as $criterion) {
        $score = (int)($_POST['score_' . $criterion['id']] ?? 0);
        if ($score >= $criterion['min_value'] && $score <= $criterion['max_value']) {
            $scores[$criterion['id']] = $score;
        }
    }
    
    if (!empty($scores)) {
        $result = $scoring->saveScores($id, $scores);
        if ($result) {
            $message = 'Skor berhasil disimpan dan total skor telah diperbarui!';
            $message_type = 'success';
            
            // Refresh data
            $existing_scores = $scoring->getScores($id);
            $score_values = [];
            foreach ($existing_scores as $score) {
                $score_values[$score['criteria_id']] = $score['score'];
            }
            $house = $boardingHouse->getById($id);
        } else {
            $message = 'Gagal menyimpan skor. Silakan coba lagi.';
            $message_type = 'danger';
        }
    } else {
        $message = 'Tidak ada skor yang valid untuk disimpan.';
        $message_type = 'warning';
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Atur Skor Kost - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-tachometer-alt"></i> Admin Panel
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Lihat Website
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="boarding-houses.php">
                                <i class="fas fa-home"></i> Kelola Kost
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="criteria.php">
                                <i class="fas fa-list-check"></i> Kriteria Penilaian
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reviews.php">
                                <i class="fas fa-comments"></i> Kelola Review
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i> Laporan
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Atur Skor Kost</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="boarding-houses.php" class="btn btn-sm btn-secondary">
                                <i class="fas fa-arrow-left"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Boarding House Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-home"></i> Informasi Kost
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4><?php echo htmlspecialchars($house['name']); ?></h4>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt"></i> 
                                    <?php echo htmlspecialchars($house['address']); ?>
                                </p>
                                <p class="mb-0">
                                    <span class="badge bg-success">Rp <?php echo number_format($house['monthly_price'], 0, ',', '.'); ?>/bulan</span>
                                    <?php if ($house['distance_to_campus']): ?>
                                    <span class="badge bg-info"><?php echo number_format($house['distance_to_campus'], 1); ?> km ke kampus</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="display-6 text-primary"><?php echo number_format($house['total_score'], 1); ?></div>
                                <small class="text-muted">Skor Total Saat Ini</small>
                                <?php if ($house['review_count'] > 0): ?>
                                <br>
                                <div class="mt-2">
                                    <i class="fas fa-star text-warning"></i> 
                                    <?php echo number_format($house['avg_rating'], 1); ?> 
                                    (<?php echo $house['review_count']; ?> review)
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scoring Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star"></i> Penilaian Berdasarkan Kriteria
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="row">
                                <?php foreach ($criteria as $criterion): ?>
                                <div class="col-lg-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <?php echo htmlspecialchars($criterion['name']); ?>
                                                <span class="badge bg-secondary">Bobot: <?php echo $criterion['weight']; ?></span>
                                            </h6>
                                            
                                            <?php if ($criterion['description']): ?>
                                            <p class="card-text text-muted small">
                                                <?php echo htmlspecialchars($criterion['description']); ?>
                                            </p>
                                            <?php endif; ?>
                                            
                                            <div class="mb-3">
                                                <label for="score_<?php echo $criterion['id']; ?>" class="form-label">
                                                    Skor (<?php echo $criterion['min_value']; ?>-<?php echo $criterion['max_value']; ?>)
                                                </label>
                                                <input type="range" 
                                                       class="form-range" 
                                                       name="score_<?php echo $criterion['id']; ?>" 
                                                       id="score_<?php echo $criterion['id']; ?>"
                                                       min="<?php echo $criterion['min_value']; ?>" 
                                                       max="<?php echo $criterion['max_value']; ?>" 
                                                       value="<?php echo $score_values[$criterion['id']] ?? $criterion['min_value']; ?>"
                                                       oninput="updateScoreDisplay(<?php echo $criterion['id']; ?>, this.value)">
                                                
                                                <div class="d-flex justify-content-between">
                                                    <small class="text-muted">Buruk</small>
                                                    <span class="badge bg-primary" id="display_<?php echo $criterion['id']; ?>">
                                                        <?php echo $score_values[$criterion['id']] ?? $criterion['min_value']; ?>
                                                    </span>
                                                    <small class="text-muted">Sangat Baik</small>
                                                </div>
                                            </div>
                                            
                                            <!-- Score descriptions -->
                                            <div class="small text-muted">
                                                <div><strong>1:</strong> Sangat Buruk</div>
                                                <div><strong>2:</strong> Buruk</div>
                                                <div><strong>3:</strong> Cukup</div>
                                                <div><strong>4:</strong> Baik</div>
                                                <div><strong>5:</strong> Sangat Baik</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <hr>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Informasi Perhitungan Skor:</h6>
                                <ul class="mb-0">
                                    <li>Skor total dihitung berdasarkan weighted average dari semua kriteria</li>
                                    <li>Review dari mahasiswa akan mempengaruhi 30% dari skor total</li>
                                    <li>Skor akan otomatis diperbarui setelah disimpan</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="boarding-houses.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Simpan Skor
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    
    <script>
        function updateScoreDisplay(criteriaId, value) {
            document.getElementById('display_' + criteriaId).textContent = value;
        }
    </script>
</body>
</html>
