<?php
/**
 * BoardingHouse Class
 * Kelas untuk mengelola data kost
 */

require_once '../config/config.php';

class BoardingHouse {
    private $conn;
    
    public function __construct() {
        $this->conn = getDB();
    }
    
    // Create new boarding house
    public function create($data) {
        try {
            $sql = "INSERT INTO boarding_houses (name, address, latitude, longitude, distance_to_campus, 
                    monthly_price, description, facilities, contact_phone, contact_whatsapp, owner_name, images) 
                    VALUES (:name, :address, :latitude, :longitude, :distance_to_campus, :monthly_price, 
                    :description, :facilities, :contact_phone, :contact_whatsapp, :owner_name, :images)";
            
            $stmt = $this->conn->prepare($sql);
            
            // Calculate distance to campus if coordinates provided
            $distance = null;
            if ($data['latitude'] && $data['longitude']) {
                $distance = calculate_distance(
                    $data['latitude'], $data['longitude'],
                    CAMPUS_LATITUDE, CAMPUS_LONGITUDE
                );
            }
            
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':latitude', $data['latitude']);
            $stmt->bindParam(':longitude', $data['longitude']);
            $stmt->bindParam(':distance_to_campus', $distance);
            $stmt->bindParam(':monthly_price', $data['monthly_price']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':facilities', json_encode($data['facilities']));
            $stmt->bindParam(':contact_phone', $data['contact_phone']);
            $stmt->bindParam(':contact_whatsapp', $data['contact_whatsapp']);
            $stmt->bindParam(':owner_name', $data['owner_name']);
            $stmt->bindParam(':images', json_encode($data['images']));
            
            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error creating boarding house: " . $e->getMessage());
            return false;
        }
    }
    
    // Get all boarding houses with filters
    public function getAll($filters = [], $sort = 'total_score', $order = 'DESC', $limit = null, $offset = 0) {
        try {
            $sql = "SELECT bh.*, 
                    (SELECT AVG(rating) FROM reviews WHERE boarding_house_id = bh.id AND is_approved = 1) as avg_rating,
                    (SELECT COUNT(*) FROM reviews WHERE boarding_house_id = bh.id AND is_approved = 1) as review_count
                    FROM boarding_houses bh WHERE bh.is_active = 1";
            
            $params = [];
            
            // Apply filters
            if (!empty($filters['min_price'])) {
                $sql .= " AND bh.monthly_price >= :min_price";
                $params[':min_price'] = $filters['min_price'];
            }
            
            if (!empty($filters['max_price'])) {
                $sql .= " AND bh.monthly_price <= :max_price";
                $params[':max_price'] = $filters['max_price'];
            }
            
            if (!empty($filters['max_distance'])) {
                $sql .= " AND bh.distance_to_campus <= :max_distance";
                $params[':max_distance'] = $filters['max_distance'];
            }
            
            if (!empty($filters['min_score'])) {
                $sql .= " AND bh.total_score >= :min_score";
                $params[':min_score'] = $filters['min_score'];
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (bh.name LIKE :search OR bh.address LIKE :search)";
                $params[':search'] = '%' . $filters['search'] . '%';
            }
            
            if (!empty($filters['facilities'])) {
                foreach ($filters['facilities'] as $facility) {
                    $sql .= " AND JSON_CONTAINS(bh.facilities, '\"" . $facility . "\"')";
                }
            }
            
            // Add sorting
            $allowed_sorts = ['name', 'monthly_price', 'distance_to_campus', 'total_score', 'created_at'];
            if (in_array($sort, $allowed_sorts)) {
                $sql .= " ORDER BY bh." . $sort . " " . ($order === 'ASC' ? 'ASC' : 'DESC');
            }
            
            // Add pagination
            if ($limit) {
                $sql .= " LIMIT :limit OFFSET :offset";
                $params[':limit'] = $limit;
                $params[':offset'] = $offset;
            }
            
            $stmt = $this->conn->prepare($sql);
            
            // Bind parameters
            foreach ($params as $key => $value) {
                if ($key === ':limit' || $key === ':offset') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting boarding houses: " . $e->getMessage());
            return [];
        }
    }
    
    // Get boarding house by ID
    public function getById($id) {
        try {
            $sql = "SELECT bh.*, 
                    (SELECT AVG(rating) FROM reviews WHERE boarding_house_id = bh.id AND is_approved = 1) as avg_rating,
                    (SELECT COUNT(*) FROM reviews WHERE boarding_house_id = bh.id AND is_approved = 1) as review_count
                    FROM boarding_houses bh WHERE bh.id = :id AND bh.is_active = 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting boarding house: " . $e->getMessage());
            return false;
        }
    }
    
    // Update boarding house
    public function update($id, $data) {
        try {
            $sql = "UPDATE boarding_houses SET name = :name, address = :address, 
                    latitude = :latitude, longitude = :longitude, monthly_price = :monthly_price,
                    description = :description, facilities = :facilities, contact_phone = :contact_phone,
                    contact_whatsapp = :contact_whatsapp, owner_name = :owner_name, images = :images,
                    updated_at = CURRENT_TIMESTAMP WHERE id = :id";
            
            $stmt = $this->conn->prepare($sql);
            
            // Calculate distance if coordinates changed
            $distance = null;
            if ($data['latitude'] && $data['longitude']) {
                $distance = calculate_distance(
                    $data['latitude'], $data['longitude'],
                    CAMPUS_LATITUDE, CAMPUS_LONGITUDE
                );
                
                $sql = "UPDATE boarding_houses SET name = :name, address = :address, 
                        latitude = :latitude, longitude = :longitude, distance_to_campus = :distance,
                        monthly_price = :monthly_price, description = :description, facilities = :facilities, 
                        contact_phone = :contact_phone, contact_whatsapp = :contact_whatsapp, 
                        owner_name = :owner_name, images = :images, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
                
                $stmt = $this->conn->prepare($sql);
                $stmt->bindParam(':distance', $distance);
            }
            
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':latitude', $data['latitude']);
            $stmt->bindParam(':longitude', $data['longitude']);
            $stmt->bindParam(':monthly_price', $data['monthly_price']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':facilities', json_encode($data['facilities']));
            $stmt->bindParam(':contact_phone', $data['contact_phone']);
            $stmt->bindParam(':contact_whatsapp', $data['contact_whatsapp']);
            $stmt->bindParam(':owner_name', $data['owner_name']);
            $stmt->bindParam(':images', json_encode($data['images']));
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating boarding house: " . $e->getMessage());
            return false;
        }
    }
    
    // Delete boarding house (soft delete)
    public function delete($id) {
        try {
            $sql = "UPDATE boarding_houses SET is_active = 0 WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error deleting boarding house: " . $e->getMessage());
            return false;
        }
    }
    
    // Get total count for pagination
    public function getCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM boarding_houses WHERE is_active = 1";
            $params = [];
            
            // Apply same filters as getAll method
            if (!empty($filters['min_price'])) {
                $sql .= " AND monthly_price >= :min_price";
                $params[':min_price'] = $filters['min_price'];
            }
            
            if (!empty($filters['max_price'])) {
                $sql .= " AND monthly_price <= :max_price";
                $params[':max_price'] = $filters['max_price'];
            }
            
            if (!empty($filters['max_distance'])) {
                $sql .= " AND distance_to_campus <= :max_distance";
                $params[':max_distance'] = $filters['max_distance'];
            }
            
            if (!empty($filters['min_score'])) {
                $sql .= " AND total_score >= :min_score";
                $params[':min_score'] = $filters['min_score'];
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (name LIKE :search OR address LIKE :search)";
                $params[':search'] = '%' . $filters['search'] . '%';
            }
            
            $stmt = $this->conn->prepare($sql);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['total'];
        } catch (PDOException $e) {
            error_log("Error getting boarding house count: " . $e->getMessage());
            return 0;
        }
    }
}
?>
