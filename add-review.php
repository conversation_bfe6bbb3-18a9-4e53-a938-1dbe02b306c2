<?php
/**
 * Add Review Page
 * Halaman untuk menambahkan review kost
 */

require_once 'config/config.php';
require_once 'classes/BoardingHouse.php';
require_once 'classes/Review.php';

$boardingHouse = new BoardingHouse();
$review = new Review();

// Get boarding house ID
$boarding_house_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$house = null;

if ($boarding_house_id) {
    $house = $boardingHouse->getById($boarding_house_id);
}

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'boarding_house_id' => (int)$_POST['boarding_house_id'],
        'reviewer_name' => sanitize_input($_POST['reviewer_name']),
        'reviewer_email' => sanitize_input($_POST['reviewer_email']),
        'student_id' => sanitize_input($_POST['student_id']),
        'rating' => (int)$_POST['rating'],
        'review_text' => sanitize_input($_POST['review_text']),
        'lived_duration_months' => (int)$_POST['lived_duration_months']
    ];
    
    // Validation
    $errors = [];
    
    if (empty($data['reviewer_name'])) {
        $errors[] = 'Nama reviewer harus diisi';
    }
    
    if (empty($data['reviewer_email']) || !filter_var($data['reviewer_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Email valid harus diisi';
    }
    
    if ($data['rating'] < 1 || $data['rating'] > 5) {
        $errors[] = 'Rating harus antara 1-5';
    }
    
    if ($data['lived_duration_months'] < 0) {
        $errors[] = 'Durasi tinggal tidak valid';
    }
    
    // Check if user already reviewed this boarding house
    if ($review->hasUserReviewed($data['boarding_house_id'], $data['student_id'], $data['reviewer_email'])) {
        $errors[] = 'Anda sudah memberikan review untuk kost ini';
    }
    
    if (empty($errors)) {
        $result = $review->create($data);
        if ($result) {
            $message = 'Review berhasil ditambahkan! Terima kasih atas kontribusi Anda.';
            $message_type = 'success';
            
            // Redirect to detail page after 2 seconds
            header("refresh:2;url=detail.php?id=" . $data['boarding_house_id']);
        } else {
            $message = 'Terjadi kesalahan saat menyimpan review. Silakan coba lagi.';
            $message_type = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $message_type = 'danger';
    }
}

// Get all boarding houses for dropdown if no specific house selected
$all_houses = [];
if (!$house) {
    $all_houses = $boardingHouse->getAll([], 'name', 'ASC');
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tambah Review - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-home"></i> KostEval
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-star"></i> Tambah Review Kost
                        </h4>
                        <?php if ($house): ?>
                        <p class="mb-0 mt-2 text-muted">
                            Review untuk: <strong><?php echo htmlspecialchars($house['name']); ?></strong>
                        </p>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <form method="POST" class="needs-validation" novalidate>
                            <?php if ($house): ?>
                                <input type="hidden" name="boarding_house_id" value="<?php echo $house['id']; ?>">
                            <?php else: ?>
                                <!-- Boarding House Selection -->
                                <div class="mb-3">
                                    <label for="boarding_house_id" class="form-label">Pilih Kost <span class="text-danger">*</span></label>
                                    <select class="form-select" name="boarding_house_id" id="boarding_house_id" required>
                                        <option value="">-- Pilih Kost --</option>
                                        <?php foreach ($all_houses as $h): ?>
                                        <option value="<?php echo $h['id']; ?>" <?php echo (isset($_POST['boarding_house_id']) && $_POST['boarding_house_id'] == $h['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($h['name']); ?> - <?php echo htmlspecialchars(substr($h['address'], 0, 50)); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Silakan pilih kost yang ingin direview.
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Reviewer Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reviewer_name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="reviewer_name" id="reviewer_name" 
                                               value="<?php echo htmlspecialchars($_POST['reviewer_name'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">
                                            Nama lengkap harus diisi.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reviewer_email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" name="reviewer_email" id="reviewer_email" 
                                               value="<?php echo htmlspecialchars($_POST['reviewer_email'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">
                                            Email valid harus diisi.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="student_id" class="form-label">NPM (Opsional)</label>
                                        <input type="text" class="form-control" name="student_id" id="student_id" 
                                               value="<?php echo htmlspecialchars($_POST['student_id'] ?? ''); ?>" 
                                               placeholder="Contoh: 2021010001">
                                        <div class="form-text">NPM Universitas Pamulang (jika ada)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lived_duration_months" class="form-label">Lama Tinggal (Bulan)</label>
                                        <input type="number" class="form-control" name="lived_duration_months" id="lived_duration_months" 
                                               value="<?php echo htmlspecialchars($_POST['lived_duration_months'] ?? ''); ?>" 
                                               min="0" max="120" placeholder="0">
                                        <div class="form-text">Berapa lama Anda tinggal di kost ini?</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Rating -->
                            <div class="mb-4">
                                <label class="form-label">Rating <span class="text-danger">*</span></label>
                                <div class="rating-container">
                                    <div class="rating-stars mb-2" style="font-size: 2rem;">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star text-muted" data-rating="<?php echo $i; ?>" style="cursor: pointer;"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <input type="hidden" name="rating" id="rating" value="<?php echo $_POST['rating'] ?? ''; ?>" required>
                                    <div class="form-text">Klik bintang untuk memberikan rating (1-5)</div>
                                    <div class="invalid-feedback">
                                        Silakan berikan rating.
                                    </div>
                                </div>
                            </div>

                            <!-- Review Text -->
                            <div class="mb-4">
                                <label for="review_text" class="form-label">Review</label>
                                <textarea class="form-control" name="review_text" id="review_text" rows="5" 
                                          placeholder="Ceritakan pengalaman Anda tinggal di kost ini..."><?php echo htmlspecialchars($_POST['review_text'] ?? ''); ?></textarea>
                                <div class="form-text">Bagikan pengalaman Anda untuk membantu mahasiswa lain</div>
                            </div>

                            <!-- Guidelines -->
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Panduan Review:</h6>
                                <ul class="mb-0">
                                    <li>Berikan review yang jujur dan objektif</li>
                                    <li>Fokus pada aspek-aspek penting seperti kebersihan, keamanan, fasilitas</li>
                                    <li>Hindari kata-kata yang menyinggung atau tidak pantas</li>
                                    <li>Review akan ditampilkan setelah disetujui admin</li>
                                </ul>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="<?php echo $house ? 'detail.php?id=' . $house['id'] : 'index.php'; ?>" 
                                   class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times"></i> Batal
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Kirim Review
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="text-center">
                <small>&copy; 2024 <?php echo APP_NAME; ?>. Dikembangkan untuk Universitas Pamulang.</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
    // Rating stars functionality
    document.addEventListener('DOMContentLoaded', function() {
        const stars = document.querySelectorAll('.rating-stars .fa-star');
        const ratingInput = document.getElementById('rating');
        
        stars.forEach((star, index) => {
            star.addEventListener('click', function() {
                const rating = index + 1;
                ratingInput.value = rating;
                
                // Update visual stars
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.remove('text-muted');
                        s.classList.add('text-warning');
                    } else {
                        s.classList.remove('text-warning');
                        s.classList.add('text-muted');
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                const hoverRating = index + 1;
                stars.forEach((s, i) => {
                    if (i < hoverRating) {
                        s.classList.add('text-warning');
                        s.classList.remove('text-muted');
                    } else {
                        s.classList.add('text-muted');
                        s.classList.remove('text-warning');
                    }
                });
            });
        });
        
        // Reset on mouse leave
        document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingInput.value) || 0;
            stars.forEach((s, i) => {
                if (i < currentRating) {
                    s.classList.add('text-warning');
                    s.classList.remove('text-muted');
                } else {
                    s.classList.add('text-muted');
                    s.classList.remove('text-warning');
                }
            });
        });
        
        // Set initial rating if exists
        const initialRating = parseInt(ratingInput.value) || 0;
        if (initialRating > 0) {
            stars.forEach((s, i) => {
                if (i < initialRating) {
                    s.classList.add('text-warning');
                    s.classList.remove('text-muted');
                }
            });
        }
    });
    </script>
</body>
</html>
