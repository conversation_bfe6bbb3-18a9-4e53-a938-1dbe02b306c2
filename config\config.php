<?php
/**
 * Application Configuration
 * Konfigurasi aplikasi untuk Sistem Penilaian Kelayakan Kost
 */

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'Sistem Penilaian Kelayakan Kost');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/Projek%20Bowok/');

// Database settings
define('DB_HOST', 'localhost');
define('DB_NAME', 'kost_evaluation_system');
define('DB_USER', 'root');
define('DB_PASS', '');

// File upload settings
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Pagination settings
define('ITEMS_PER_PAGE', 12);

// Google Maps API (replace with your API key)
define('GOOGLE_MAPS_API_KEY', 'YOUR_GOOGLE_MAPS_API_KEY');

// Pamulang University coordinates
define('CAMPUS_LATITUDE', -6.3373);
define('CAMPUS_LONGITUDE', 106.7537);

// Scoring system settings
define('MAX_SCORE', 100);
define('REVIEW_WEIGHT', 0.3); // 30% weight for reviews in total score

// Security settings
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour

// Include database connection
require_once 'database.php';

// Utility functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header("Location: " . BASE_URL . $url);
    exit();
}

function is_admin_logged_in() {
    return isset($_SESSION['admin_id']) && 
           isset($_SESSION['admin_username']) &&
           (time() - $_SESSION['last_activity']) < ADMIN_SESSION_TIMEOUT;
}

function require_admin_login() {
    if (!is_admin_logged_in()) {
        redirect('admin/login.php');
    }
    $_SESSION['last_activity'] = time();
}

function calculate_distance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 6371; // Earth radius in kilometers
    
    $lat1_rad = deg2rad($lat1);
    $lon1_rad = deg2rad($lon1);
    $lat2_rad = deg2rad($lat2);
    $lon2_rad = deg2rad($lon2);
    
    $delta_lat = $lat2_rad - $lat1_rad;
    $delta_lon = $lon2_rad - $lon1_rad;
    
    $a = sin($delta_lat/2) * sin($delta_lat/2) + 
         cos($lat1_rad) * cos($lat2_rad) * 
         sin($delta_lon/2) * sin($delta_lon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earth_radius * $c;
}
?>
