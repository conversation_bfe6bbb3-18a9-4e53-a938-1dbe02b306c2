# Deployment Guide - Sistem Penilaian Kelayakan Kost

Panduan deployment aplikasi ke production server.

## Pre-Deployment Checklist

### 1. Code Review
- [ ] Semua fitur telah ditest dan berfungsi
- [ ] Tidak ada hardcoded credentials
- [ ] Error handling implemented
- [ ] Input validation complete
- [ ] Security measures in place

### 2. Configuration
- [ ] Database credentials configured
- [ ] Google Maps API key set (production key)
- [ ] Base URL updated for production
- [ ] File upload permissions set
- [ ] Error logging configured

### 3. Database Preparation
- [ ] Production database created
- [ ] Schema imported successfully
- [ ] Sample data imported (optional)
- [ ] Database user privileges set correctly

## Deployment Options

### Option 1: Shared Hosting (Recommended for beginners)

#### Requirements
- PHP 7.4+ support
- MySQL database
- File manager or FTP access
- At least 100MB storage

#### Steps
1. **Prepare Files**
   ```bash
   # Create deployment package
   zip -r kost-evaluation-system.zip . -x "*.git*" "*.md" "TESTING.md"
   ```

2. **Upload Files**
   - Upload zip file to hosting
   - Extract to public_html or www directory
   - Set folder permissions: 755
   - Set file permissions: 644

3. **Database Setup**
   - Create MySQL database via hosting panel
   - Import `database/schema.sql`
   - Import `database/sample_data.sql` (optional)
   - Note database credentials

4. **Configuration**
   - Edit `config/config.php`:
   ```php
   define('DB_HOST', 'localhost'); // or hosting DB server
   define('DB_NAME', 'your_db_name');
   define('DB_USER', 'your_db_user');
   define('DB_PASS', 'your_db_password');
   define('BASE_URL', 'https://yourdomain.com/');
   ```

5. **Test Deployment**
   - Access website URL
   - Test basic functionality
   - Login to admin panel
   - Verify database connections

### Option 2: VPS/Cloud Server

#### Requirements
- Ubuntu 20.04+ or CentOS 8+
- 1GB+ RAM
- 10GB+ storage
- Root or sudo access

#### Steps
1. **Server Setup**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install LAMP stack
   sudo apt install apache2 mysql-server php php-mysql php-mbstring php-xml php-curl -y
   
   # Enable Apache modules
   sudo a2enmod rewrite
   sudo systemctl restart apache2
   ```

2. **Database Setup**
   ```bash
   # Secure MySQL installation
   sudo mysql_secure_installation
   
   # Create database and user
   sudo mysql -u root -p
   ```
   ```sql
   CREATE DATABASE kost_evaluation_system;
   CREATE USER 'kostuser'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON kost_evaluation_system.* TO 'kostuser'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

3. **Deploy Application**
   ```bash
   # Clone repository
   cd /var/www/html
   sudo git clone [repository-url] kost-eval
   
   # Set permissions
   sudo chown -R www-data:www-data kost-eval/
   sudo chmod -R 755 kost-eval/
   sudo chmod -R 777 kost-eval/uploads/
   ```

4. **Apache Configuration**
   ```bash
   # Create virtual host
   sudo nano /etc/apache2/sites-available/kost-eval.conf
   ```
   ```apache
   <VirtualHost *:80>
       ServerName yourdomain.com
       DocumentRoot /var/www/html/kost-eval
       
       <Directory /var/www/html/kost-eval>
           AllowOverride All
           Require all granted
       </Directory>
       
       ErrorLog ${APACHE_LOG_DIR}/kost-eval_error.log
       CustomLog ${APACHE_LOG_DIR}/kost-eval_access.log combined
   </VirtualHost>
   ```
   
   ```bash
   # Enable site
   sudo a2ensite kost-eval.conf
   sudo a2dissite 000-default.conf
   sudo systemctl restart apache2
   ```

5. **SSL Certificate (Recommended)**
   ```bash
   # Install Certbot
   sudo apt install certbot python3-certbot-apache -y
   
   # Get SSL certificate
   sudo certbot --apache -d yourdomain.com
   ```

### Option 3: Docker Deployment

#### Docker Compose Setup
Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "80:80"
    volumes:
      - ./uploads:/var/www/html/uploads
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_NAME=kost_evaluation_system
      - DB_USER=kostuser
      - DB_PASS=kostpass

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
      - MYSQL_DATABASE=kost_evaluation_system
      - MYSQL_USER=kostuser
      - MYSQL_PASSWORD=kostpass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d

volumes:
  mysql_data:
```

Create `Dockerfile`:
```dockerfile
FROM php:8.1-apache

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql

# Enable Apache rewrite module
RUN a2enmod rewrite

# Copy application files
COPY . /var/www/html/

# Set permissions
RUN chown -R www-data:www-data /var/www/html/
RUN chmod -R 755 /var/www/html/
RUN chmod -R 777 /var/www/html/uploads/

EXPOSE 80
```

Deploy with:
```bash
docker-compose up -d
```

## Post-Deployment Tasks

### 1. Security Hardening
- [ ] Change default admin password
- [ ] Remove or secure phpinfo files
- [ ] Set proper file permissions
- [ ] Configure firewall rules
- [ ] Enable HTTPS
- [ ] Set up regular backups

### 2. Performance Optimization
- [ ] Enable PHP OPcache
- [ ] Configure Apache/Nginx caching
- [ ] Optimize database queries
- [ ] Compress static assets
- [ ] Set up CDN (if needed)

### 3. Monitoring Setup
- [ ] Configure error logging
- [ ] Set up uptime monitoring
- [ ] Database performance monitoring
- [ ] Disk space monitoring

### 4. Backup Strategy
```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u kostuser -p kost_evaluation_system > backup_$DATE.sql
gzip backup_$DATE.sql

# File backup
tar -czf files_backup_$DATE.tar.gz /var/www/html/kost-eval/uploads/
```

## Production Configuration

### 1. PHP Configuration (php.ini)
```ini
; Error reporting (disable in production)
display_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; File uploads
upload_max_filesize = 10M
post_max_size = 10M
max_file_uploads = 20

; Security
expose_php = Off
allow_url_fopen = Off
```

### 2. Apache Configuration (.htaccess)
```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Prevent access to sensitive files
<Files "config.php">
    Require all denied
</Files>

<Files "*.sql">
    Require all denied
</Files>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

## Troubleshooting Common Issues

### 1. Database Connection Issues
```
Error: "Connection refused"
Solution: Check database server status, credentials, and firewall
```

### 2. Permission Issues
```
Error: "Permission denied"
Solution: Set correct file/folder permissions (755/644)
```

### 3. PHP Errors
```
Error: "Call to undefined function"
Solution: Install required PHP extensions
```

### 4. Apache Issues
```
Error: "Internal Server Error"
Solution: Check Apache error logs, .htaccess syntax
```

## Maintenance

### Regular Tasks
- [ ] Weekly database backups
- [ ] Monthly security updates
- [ ] Quarterly performance review
- [ ] Annual SSL certificate renewal

### Monitoring Commands
```bash
# Check disk space
df -h

# Check Apache status
sudo systemctl status apache2

# Check MySQL status
sudo systemctl status mysql

# View error logs
sudo tail -f /var/log/apache2/error.log
sudo tail -f /var/log/mysql/error.log
```

## Rollback Plan

### Emergency Rollback
1. Keep previous version backup
2. Database rollback script ready
3. DNS change procedure documented
4. Downtime notification prepared

### Rollback Steps
```bash
# Stop services
sudo systemctl stop apache2

# Restore files
sudo rm -rf /var/www/html/kost-eval
sudo tar -xzf backup_files.tar.gz -C /var/www/html/

# Restore database
mysql -u kostuser -p kost_evaluation_system < backup_database.sql

# Restart services
sudo systemctl start apache2
```

## Support Contacts

- **Technical Issues:** [tech-support-email]
- **Server Issues:** [server-admin-email]
- **Emergency Contact:** [emergency-phone]
